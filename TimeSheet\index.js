import { registerRootComponent } from 'expo';
import { LogBox, AppState, Platform, Alert } from 'react-native';
// Import Firebase configuration to ensure it's initialized early
import { FIREBASE_APP, FIREBASE_AUTH, FIRESTORE_DB } from './src/firebaseConfig';
import App from './App';

// More comprehensive warning ignoring for SDK 53
LogBox.ignoreLogs([
  // Firebase related
  'Setting a timer',
  'AsyncStorage has been extracted from react-native core',
  // React Navigation related
  'Non-serializable values were found in the navigation state',
  // Metro bundler related
  'Require cycle:',
  // Reanimated related
  'Sending `onAnimatedValueUpdate` with no listeners registered',
  // Expo related
  'Constants.platform.ios.model has been deprecated',
  // Misc warnings that might appear in SDK 53
  'ViewPropTypes will be removed from React Native',
  'Possible Unhandled Promise Rejection',
]);

// Enhanced error handling for SDK 53
const errorHandler = (error, isFatal) => {
  if (isFatal) {
    console.error('FATAL ERROR:', error);

    // Show a user-friendly error message
    if (Platform.OS === 'android') {
      // On Android, we can use Alert
      Alert.alert(
        'Unexpected Error',
        'The application encountered an unexpected error. Please restart the app.',
        [{ text: 'OK' }]
      );
    } else {
      // On iOS, Alert might not work in this context, so just log
      console.error('FATAL iOS ERROR - App needs restart');
    }
  } else {
    console.warn('NON-FATAL ERROR:', error);
  }
};

// Set up the global error handler
global.ErrorUtils.setGlobalHandler(errorHandler);

// Ensure Firebase Auth is initialized
console.log('Firebase Auth initialized:', FIREBASE_AUTH ? 'Yes' : 'No');
console.log('Running on SDK 53 with enhanced error handling');

// Monitor app state for debugging
AppState.addEventListener('change', (nextAppState) => {
  console.log('App State changed to:', nextAppState);
});

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
