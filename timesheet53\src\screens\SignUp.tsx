import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, Image, TouchableOpacity, Alert } from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../config/firebase';
import { createUserWithEmailAndPassword, sendEmailVerification } from 'firebase/auth';
import { Ionicons } from "@expo/vector-icons";
import { doc, setDoc } from 'firebase/firestore';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types/navigation';

type SignUpScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'SignUp'>;

interface SignUpProps {
    navigation: SignUpScreenNavigationProp;
}

export default function SignUp({ navigation }: SignUpProps) {
    const [username, setUsername] = useState<string>('');
    const [email, setEmail] = useState<string>('');
    const [password, setPassword] = useState<string>('');
    const [role, setRole] = useState<string>('user');
    const [message, setMessage] = useState<string>('');
    const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
    const [passwordVisible, setPasswordVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);

    const validateEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const validatePassword = (password: string): boolean => {
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        return passwordRegex.test(password);
    };

    const handleSignUp = async (): Promise<void> => {
        try {
            setLoading(true);

            if (username === '') {
                setMessage('Please enter your username');
                setMessageType('error');
                return;
            }

            if (!validateEmail(email)) {
                setMessage('Please provide a valid email address.');
                setMessageType('error');
                return;
            }

            if (!validatePassword(password)) {
                setMessage('Password must have at least: \n - 1 capital letter \n - 1 small letter \n - 1 number \n - 1 special character \n - 8 characters long password');
                setMessageType('error');
                return;
            }

            const userCredential = await createUserWithEmailAndPassword(
                FIREBASE_AUTH,
                email,
                password
            );

            // Send email verification
            await sendEmailVerification(userCredential.user);

            // Create user document with role
            await setDoc(doc(FIRESTORE_DB, 'users', userCredential.user.uid), {
                username: username,
                email: email.toLowerCase(),
                role: 'user',
                emailVerified: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });

            setMessage('Account created successfully! Please check your email to verify your account.');
            setMessageType('success');
            
            // Navigate to login page after successful signup
            setTimeout(() => {
                navigation.navigate('Login');
            }, 2000);
        } catch (error) {
            console.error(error);
            if (error instanceof Error) {
                if (error.message.includes('email-already-in-use')) {
                    setMessage('This email is already registered. Please use a different email or try logging in.');
                } else if (error.message.includes('invalid-email')) {
                    setMessage('Please provide a valid email address.');
                } else if (error.message.includes('weak-password')) {
                    setMessage('Password is too weak. Please use a stronger password.');
                } else {
                    setMessage('An error occurred during sign up. Please try again.');
                }
                setMessageType('error');
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Image
                    source={require('../../assets/images/UCS-logo.png')}
                    style={styles.logo}
                />
            </View>

            <Text style={styles.title}>Create an account</Text>

            {message ? (
                <View style={[styles.messageBox, messageType === 'success' ? styles.successBox : styles.errorBox]}>
                    <Text style={styles.message}>{message}</Text>
                </View>
            ) : null}

            <TextInput
                style={styles.input}
                placeholder="Enter Your Username"
                value={username}
                onChangeText={setUsername}
                autoCapitalize="none"
            />

            <TextInput
                style={styles.input}
                placeholder="Enter Your Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
            />

            <View style={styles.passwordContainer}>
                <TextInput
                    style={styles.passwordInput}
                    placeholder="Enter Your Password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={!passwordVisible}
                />
                <TouchableOpacity onPress={() => setPasswordVisible(!passwordVisible)}>
                    <Ionicons
                        name={passwordVisible ? 'eye-off' : 'eye'}
                        size={24}
                        color="gray"
                        style={styles.eyeIcon}
                    />
                </TouchableOpacity>
            </View>

            <View style={styles.buttonContainer}>
                <Button 
                    title={loading ? "Signing Up..." : "Sign Up"} 
                    onPress={handleSignUp}
                    disabled={loading}
                />
            </View>

            <Text style={styles.footerText}>
                Already have an account?
                <Text onPress={() => navigation.navigate('Login')} style={styles.loginLink}> Login</Text>
            </Text>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: "flex-start",
        padding: 16,
        marginTop: 30,
    },

    header: {
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 20,
    },

    logo: {
        width: 125,
        height: 50,
        marginBottom: 20,
        alignSelf: 'center'
    },

    title: {
        fontSize: 24,
        fontWeight: "bold",
        fontFamily: "Arial",
        alignSelf: 'center',
        marginBottom: 15,
    },

    input: {
        borderWidth: 1,
        padding: 8,
        marginBottom: 26,
        borderRadius: 10,
        width: '90%',
        alignSelf: 'center'
    },

    passwordContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 10,
        width: '90%',
        alignSelf: 'center',
        paddingRight: 10,
        marginBottom: 20,
    },

    passwordInput: {
        flex: 1,
        padding: 10,
    },

    eyeIcon: {
        paddingHorizontal: 5,
    },

    buttonContainer: {
        width: '80%',
        height: 50,
        marginBottom: 10,
        marginTop: 10,
        justifyContent: 'center',
        alignSelf: 'center',
    },

    messageBox: {
        padding: 10,
        borderRadius: 10,
        marginBottom: 20,
        alignSelf: 'center',
        width: '90%',
    },

    successBox: {
        backgroundColor: 'rgba(0, 255, 0, 0.2)',
    },

    errorBox: {
        backgroundColor: 'rgba(255, 0, 0, 0.2)',
    },

    message: {
        textAlign: 'center',
        color: '#000',
    },

    footerText: {
        textAlign: 'center',
        marginTop: 16,
    },

    loginLink: {
        color: 'darkblue',
    },
}); 