const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

console.log(`${colors.cyan}=== Firebase Dependencies Fixer ====${colors.reset}`);
console.log(`${colors.cyan}This script will fix Firebase dependency issues${colors.reset}`);

// Function to execute commands and handle errors
function executeCommand(command, errorMessage, ignoreError = false) {
  try {
    console.log(`${colors.yellow}Executing: ${command}${colors.reset}`);
    execSync(command, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`${colors.red}${errorMessage}: ${error.message}${colors.reset}`);
    if (!ignoreError) {
      console.log(`${colors.yellow}Continuing despite error...${colors.reset}`);
    }
    return false;
  }
}

// Step 1: Install specific versions of problematic dependencies
console.log(`\n${colors.magenta}Step 1: Installing specific versions of dependencies${colors.reset}`);

// Install idb explicitly
executeCommand('npm install idb@7.1.1 --save-exact --legacy-peer-deps', 'Failed to install idb');

// Install firebase with a specific version
executeCommand('npm install firebase@11.2.0 --save-exact --legacy-peer-deps', 'Failed to install firebase');

// Step 2: Create a patch for the missing postinstall.mjs file
console.log(`\n${colors.magenta}Step 2: Creating patch for missing postinstall.mjs${colors.reset}`);

const utilDir = path.join(__dirname, '..', 'node_modules', '@firebase', 'util', 'dist');
const postinstallPath = path.join(utilDir, 'postinstall.mjs');

// Check if the directory exists
if (!fs.existsSync(utilDir)) {
  console.log(`${colors.red}@firebase/util/dist directory not found!${colors.reset}`);
} else {
  // Create an empty postinstall.mjs file
  console.log(`${colors.yellow}Creating empty postinstall.mjs file...${colors.reset}`);
  
  const postinstallContent = `// Empty postinstall file created by fix-firebase-deps.js
export default {};
`;
  
  try {
    fs.writeFileSync(postinstallPath, postinstallContent);
    console.log(`${colors.green}Created postinstall.mjs successfully!${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}Failed to create postinstall.mjs: ${error.message}${colors.reset}`);
  }
}

// Step 3: Fix the idb package.json if needed
console.log(`\n${colors.magenta}Step 3: Fixing idb package.json${colors.reset}`);

const idbDir = path.join(__dirname, '..', 'node_modules', 'idb');
const idbPackageJsonPath = path.join(idbDir, 'package.json');

if (!fs.existsSync(idbDir)) {
  console.log(`${colors.red}idb directory not found!${colors.reset}`);
} else if (!fs.existsSync(idbPackageJsonPath)) {
  console.log(`${colors.red}idb package.json not found!${colors.reset}`);
} else {
  try {
    // Read the current package.json
    const idbPackageJson = JSON.parse(fs.readFileSync(idbPackageJsonPath, 'utf8'));
    
    // Check if it has exports field
    if (idbPackageJson.exports) {
      console.log(`${colors.yellow}Modifying idb package.json exports...${colors.reset}`);
      
      // Simplify the exports field
      idbPackageJson.exports = {
        ".": {
          "import": "./build/index.js",
          "require": "./build/index.cjs"
        }
      };
      
      // Write the updated package.json
      fs.writeFileSync(idbPackageJsonPath, JSON.stringify(idbPackageJson, null, 2));
      console.log(`${colors.green}Updated idb package.json successfully!${colors.reset}`);
    } else {
      console.log(`${colors.blue}idb package.json doesn't have exports field, no changes needed.${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}Failed to update idb package.json: ${error.message}${colors.reset}`);
  }
}

// Step 4: Create a metro.config.js with extraNodeModules
console.log(`\n${colors.magenta}Step 4: Updating metro.config.js with extraNodeModules${colors.reset}`);

const metroConfigPath = path.join(__dirname, '..', 'metro.config.js');
const metroConfigContent = `// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add extraNodeModules to resolve Firebase dependencies
config.resolver.extraNodeModules = {
  // Ensure idb is properly resolved
  'idb': path.resolve(__dirname, 'node_modules/idb'),
};

// Add additional extensions for Firebase
config.resolver.sourceExts = ['js', 'jsx', 'ts', 'tsx', 'json', 'mjs', 'cjs'];

module.exports = config;
`;

try {
  fs.writeFileSync(metroConfigPath, metroConfigContent);
  console.log(`${colors.green}Updated metro.config.js successfully!${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Failed to update metro.config.js: ${error.message}${colors.reset}`);
}

// Final instructions
console.log(`\n${colors.green}=== Firebase Dependencies Fix Complete ===${colors.reset}`);
console.log(`${colors.cyan}To start your app with the fixed dependencies, run:${colors.reset}`);
console.log(`${colors.yellow}npm run start-metro-fix${colors.reset}`);
console.log(`\n${colors.cyan}If you still have issues, try:${colors.reset}`);
console.log(`${colors.yellow}1. npm run clear-cache${colors.reset}`);
console.log(`${colors.yellow}2. npm run start-metro-fix${colors.reset}`);
