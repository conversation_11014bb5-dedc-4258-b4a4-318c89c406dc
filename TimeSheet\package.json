{"name": "timesheet", "main": "index.js", "version": "1.0.0", "scripts": {"start": "expo start", "start-clear": "npx expo start --clear", "reset-project": "node ./scripts/reset-project.js", "fix-deps": "node ./scripts/fix-dependencies.js", "fix-firebase": "node ./scripts/fix-firebase-deps.js", "fix-idb": "node ./scripts/fix-idb.js", "fix-babel": "node ./scripts/fix-babel.js", "clear-cache": "npm install rimraf && node ./scripts/clear-metro-cache.js", "fix-metro": "node ./scripts/fix-metro-error.js", "bundle-app": "node ./scripts/bundle-app.js", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web", "test": "jest --watchAll", "lint": "npx expo lint", "doctor": "npx expo doctor", "prebuild": "npx expo prebuild --clean", "start-safe": "npx expo start --no-dev --minify", "start-metro-fix": "npx expo start --clear --no-dev", "use-minimal": "node ./scripts/switch-config.js minimal", "use-full": "node ./scripts/switch-config.js full", "fix-all": "npm run fix-deps && npm run fix-idb && npm run fix-babel && npm run clear-cache && npm run start-metro-fix"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/cli": "^0.24.13", "@expo/vector-icons": "^14.0.5", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/datetimepicker": "8.3.0", "@react-native-google-signin/google-signin": "^12.0.2", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/elements": "2.4.2", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "7.0.0", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "expo": "~53.0.0", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-location": "~18.1.5", "expo-print": "~14.1.4", "expo-router": "~5.0.5", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.0", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.0", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "firebase": "10.12.2", "idb": "7.0.1", "moment": "^2.30.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-calendar-range-picker": "^1.6.0", "react-native-calendars": "^1.1309.0", "react-native-date-picker": "^5.0.7", "react-native-dotenv": "^3.4.11", "react-native-elements": "^3.4.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-html-to-pdf": "^0.12.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-share": "^11.0.3", "react-native-vector-icons": "^10.1.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "rimraf": "^6.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-transform-async-generator-functions": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-logical-assignment-operators": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-numeric-separator": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.27.3", "@babel/plugin-transform-optional-catch-binding": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.24.7", "@babel/plugin-transform-private-property-in-object": "^7.24.7", "@react-native-community/cli": "^15.0.0", "@react-native/babel-preset": "^0.76.0", "@react-native/metro-config": "^0.76.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "19.0.0", "babel-preset-expo": "~13.0.0", "jest": "^29.7.0", "jest-expo": "53.0.0", "metro-react-native-babel-preset": "^0.76.0", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "overrides": {"@xmldom/xmldom": "0.9.0", "nanoid": "3.3.8", "undici": "^6.19.8"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["exceljs", "firebase", "idb", "moment", "react-native-calendar-range-picker", "react-native-elements", "react-native-vector-icons", "<PERSON><PERSON><PERSON>", "xlsx"], "listUnknownPackages": false}}}, "private": true, "codegenConfig": {"react-native-codegen": {"codegenModuleName": "MyAppCodegen", "libraryName": "MyApp", "libraryType": "Shared"}}}