Updated ones

AdminAttendance.js

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ScrollView, ActivityIndicator, FlatList } from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { collection, query, getDocs, where, orderBy, addDoc } from 'firebase/firestore';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addWeeks, subWeeks } from 'date-fns';
import { Icon } from 'react-native-elements';
import { Picker } from '@react-native-picker/picker';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { parse, isValid } from 'date-fns';

export default function AdminAttendance({ navigation }) {
    const [isAdmin, setIsAdmin] = useState(false);
    const [username, setUsername] = useState('');
    const [users, setUsers] = useState([]);
    const [selectedUser, setSelectedUser] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    
    // Week selection for attendance records
    const currentDate = new Date();
    const [selectedWeek, setSelectedWeek] = useState(currentDate);
    const [weeklyAttendance, setWeeklyAttendance] = useState([]);
    const [paidHolidays, setPaidHolidays] = useState({});

    useEffect(() => {
        checkAdminAccess();
        fetchUsers();
        fetchPaidHolidays();
    }, []);

    useEffect(() => {
        if (selectedUser) {
            fetchWeeklyAttendance();
        }
    }, [selectedUser, selectedWeek]);

    // Check if current user is admin
    const checkAdminAccess = async () => {
        try {
            const currentUser = FIREBASE_AUTH.currentUser;
            if (!currentUser) {
                navigation.replace('Login');
                return;
            }

            const userDoc = await getDocs(
                query(
                    collection(FIRESTORE_DB, 'users'),
                    where('email', '==', currentUser.email)
                )
            );

            if (!userDoc.empty) {
                const userData = userDoc.docs[0].data();
                setUsername(userData.username);
                setIsAdmin(userData.role === 'admin');
                if (userData.role !== 'admin') {
                    Alert.alert('Access Denied', 'Only admins can access this screen');
                    navigation.replace('Dashboard');
                }
            }
        } catch (error) {
            console.error('Error checking admin status:', error);
            Alert.alert('Error', 'Failed to verify admin access');
        }
    };

    // Fetch all users
    const fetchUsers = async () => {
        try {
            const querySnapshot = await getDocs(collection(FIRESTORE_DB, 'users'));
            const usersList = querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
            setUsers(usersList);
        } catch (error) {
            console.error('Error fetching users:', error);
            Alert.alert('Error', 'Failed to load users');
        }
    };

    // Function to fetch attendance for a specific date
    const fetchAttendanceForDate = async (formattedDate) => {
        if (!selectedUser) return null;

        try {
            // Format the date to match the document ID format
            const docId = `${selectedUser.id}_${formattedDate.replace(/\//g, '-')}`;

            // Query the attendance collection
            const q = query(
                collection(FIRESTORE_DB, 'attendance'),
                where('docId', '==', docId)
            );
            
            const querySnapshot = await getDocs(q);
            
            if (!querySnapshot.empty) {
                return querySnapshot.docs[0].data();
            } else {
                return null;
            }
        } catch (error) {
            console.error('Error fetching attendance record:', error);
            return null;
        }
    };

    // Function to fetch paid holidays
    const fetchPaidHolidays = async () => {
        try {
            const currentYear = new Date().getFullYear();
            const holidaysQuery = query(
                collection(FIRESTORE_DB, 'paidHolidays'),
                where('year', '==', currentYear)
            );
            const querySnapshot = await getDocs(holidaysQuery);
            const holidaysMap = {};
            querySnapshot.docs.forEach(doc => {
                const holiday = doc.data();
                holidaysMap[holiday.date] = holiday.description;
            });
            setPaidHolidays(holidaysMap);
        } catch (error) {
            console.error('Error fetching paid holidays:', error);
        }
    };

    // Function to fetch weekly attendance records
    const fetchWeeklyAttendance = async () => {
        if (!selectedUser) return;

        setIsLoading(true);
        try {
            // Ensure selectedWeek is a valid Date object
            const weekDate = new Date(selectedWeek);

            // Create date range for the selected week (Sunday to Saturday)
            const start = startOfWeek(weekDate, { weekStartsOn: 0 });
            const end = endOfWeek(weekDate, { weekStartsOn: 0 });

            // Get all days in the week
            const daysInWeek = eachDayOfInterval({ start, end });

            // Initialize attendance data with empty values for all days
            const initialAttendance = daysInWeek.map(day => {
                const formattedDate = format(day, 'dd-MM-yyyy');
                return {
                    date: format(day, 'yyyy-MM-dd'),
                    formattedDate: format(day, 'dd-MM'),
                    dayOfWeek: format(day, 'EEE'),
                    checkInTime: null,
                    checkOutTime: null,
                    workedHours: '-',
                    totalWorkedMinutes: 0,
                    isHoliday: !!paidHolidays[formattedDate],
                    holidayDescription: paidHolidays[formattedDate] || null
                };
            });

            // Process each day in the week
            const attendanceData = {};
            for (const day of daysInWeek) {
                try {
                    // Format the date to match what's stored in Firestore
                    const dateFormatted = day.toLocaleDateString();

                    // Fetch attendance for this specific day
                    const attendanceRecord = await fetchAttendanceForDate(dateFormatted);

                    if (attendanceRecord) {
                        // If record exists, add it to our data
                        const dateKey = format(day, 'yyyy-MM-dd');
                        attendanceData[dateKey] = {
                            checkInTime: attendanceRecord.checkInTime,
                            checkOutTime: attendanceRecord.checkOutTime,
                            workedHours: attendanceRecord.workedHours || '-',
                            totalWorkedMinutes: attendanceRecord.totalWorkedMinutes || 0
                        };
                    }
                } catch (error) {
                    console.error(`Error fetching attendance for ${format(day, 'yyyy-MM-dd')}:`, error);
                }
            }

            // Update the initialAttendance array with actual data
            const updatedAttendance = initialAttendance.map(day => {
                const dateKey = day.date;
                if (attendanceData[dateKey]) {
                    return {
                        ...day,
                        checkInTime: attendanceData[dateKey].checkInTime,
                        checkOutTime: attendanceData[dateKey].checkOutTime,
                        workedHours: attendanceData[dateKey].workedHours,
                        totalWorkedMinutes: attendanceData[dateKey].totalWorkedMinutes
                    };
                }
                return day;
            });

            setWeeklyAttendance(updatedAttendance);
        } catch (error) {
            console.error('Error fetching weekly attendance:', error);
            Alert.alert('Error', 'Failed to load attendance records');
        } finally {
            setIsLoading(false);
        }
    };

    // Function to navigate to previous week
    const goToPreviousWeek = () => {
        setSelectedWeek(prevWeek => subWeeks(prevWeek, 1));
    };

    // Function to navigate to next week
    const goToNextWeek = () => {
        setSelectedWeek(prevWeek => addWeeks(prevWeek, 1));
    };

    // Format date-time for display
    const formatDateTime = (dateTimeString) => {
        if (!dateTimeString) return '-';
        const date = new Date(dateTimeString);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    // Handle user selection
    const handleUserChange = (userId) => {
        const user = users.find(u => u.id === userId);
        setSelectedUser(user);
    };

    // Handle logout
    const handleLogout = () => {
        FIREBASE_AUTH.signOut()
            .then(() => {
                console.log('User logged out');
                navigation.navigate('Login');
            })
            .catch((error) => {
                console.error('Error logging out:', error);
            });
    };

    const handleHolidaysUpload = async () => {
        try {
            const result = await DocumentPicker.getDocumentAsync({
                type: ['text/csv', 'application/vnd.ms-excel'],
                copyToCacheDirectory: true
            });

            if (result.type === 'success') {
                setIsLoading(true);
                const fileContent = await FileSystem.readAsStringAsync(result.uri);
                const lines = fileContent.split('\n');
                
                // Skip header row
                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line) continue;

                    const [date, description] = line.split(',').map(item => item.trim());
                    if (!date || !description) continue;

                    // Parse date (DD/MM/YYYY)
                    const parsedDate = parse(date, 'dd/MM/yyyy', new Date());
                    if (!isValid(parsedDate)) {
                        console.error(`Invalid date format: ${date}`);
                        continue;
                    }

                    const formattedDate = format(parsedDate, 'dd-MM-yyyy');
                    const year = parsedDate.getFullYear();

                    // Add to Firestore
                    await addDoc(collection(FIRESTORE_DB, 'paidHolidays'), {
                        date: formattedDate,
                        description,
                        year,
                        createdAt: new Date().toISOString(),
                        createdBy: FIREBASE_AUTH.currentUser?.role
                    });
                }

                Alert.alert('Success', 'Holidays uploaded successfully');
                fetchWeeklyAttendance(); 
            }
        } catch (error) {
            console.error('Error uploading file:', error);
            Alert.alert('Error', 'Failed to upload holidays file');
        } finally {
            setIsLoading(false);
        }
    };

    // Modify the renderAttendanceRow function to include holiday styling
    const renderAttendanceRow = ({ item }) => {
        const isWeekend = item.dayOfWeek === 'Sun' || item.dayOfWeek === 'Sat';
        const backgroundColor = item.isHoliday ? '#E3F2FD' : isWeekend ? '#F5F5F5' : '#FFFFFF';

        return (
            <View style={[styles.attendanceRow, { backgroundColor }]}>
                <View style={styles.dateCell}>
                    <Text style={styles.dateText}>{item.formattedDate}</Text>
                    <Text style={styles.dayText}>{item.dayOfWeek}</Text>
                    {item.isHoliday && (
                        <Text style={styles.holidayText}>{item.holidayDescription}</Text>
                    )}
                </View>
                <Text style={styles.timeCell}>{formatDateTime(item.checkInTime)}</Text>
                <Text style={styles.timeCell}>{formatDateTime(item.checkOutTime)}</Text>
                <Text style={styles.hoursCell}>{item.workedHours}</Text>
            </View>
        );
    };

    if (!isAdmin) {
        return null;
    }

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.openDrawer()}>
                    <Icon name="menu" size={23} color="#333" />
                </TouchableOpacity>
                <Text style={styles.title}>Attendance Management</Text>
                <View style={styles.headerButtons}>
                    <TouchableOpacity onPress={handleHolidaysUpload} style={styles.headerButton}>
                        <Icon name="calendar" size={23} color="#333" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={handleLogout}>
                        <Icon name="logout" size={23} color="#333" />
                    </TouchableOpacity>
                </View>
            </View>

            <View style={styles.userSelectionContainer}>
                <Text style={styles.sectionTitle}>Select Employee:</Text>
                <View style={styles.pickerContainer}>
                    <Picker
                        selectedValue={selectedUser?.id}
                        onValueChange={handleUserChange}
                        style={styles.picker}
                    >
                        <Picker.Item label="Select an employee" value="" />
                        {users.map(user => (
                            <Picker.Item key={user.id} label={user.username} value={user.id} />
                        ))}
                    </Picker>
                </View>
            </View>

            {selectedUser && (
                <View style={styles.attendanceContainer}>
                    <Text style={styles.sectionTitle}>
                        Attendance Records for {selectedUser.username}
                    </Text>

                    <View style={styles.weekSelector}>
                        <TouchableOpacity onPress={goToPreviousWeek} style={styles.weekButton}>
                            <Icon name="chevron-left" size={24} color="#007AFF" />
                        </TouchableOpacity>
                        <Text style={styles.weekText}>
                            {format(startOfWeek(selectedWeek, { weekStartsOn: 0 }), 'dd MMM')} - {format(endOfWeek(selectedWeek, { weekStartsOn: 0 }), 'dd MMM yyyy')}
                        </Text>
                        <TouchableOpacity onPress={goToNextWeek} style={styles.weekButton}>
                            <Icon name="chevron-right" size={24} color="#007AFF" />
                        </TouchableOpacity>
                    </View>

                    {isLoading ? (
                        <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
                    ) : (
                        <ScrollView horizontal={true} style={styles.tableContainer}>
                            <View>
                                <View style={styles.tableHeader}>
                                    <Text style={[styles.headerCell, { width: 80 }]}>Date</Text>
                                    <Text style={[styles.headerCell, { width: 80 }]}>Day</Text>
                                    <Text style={[styles.headerCell, { width: 100 }]}>Check In</Text>
                                    <Text style={[styles.headerCell, { width: 100 }]}>Check Out</Text>
                                    <Text style={[styles.headerCell, { width: 120 }]}>Worked Hours</Text>
                                </View>
                                <FlatList
                                    data={weeklyAttendance}
                                    keyExtractor={(item) => item.date}
                                    // renderItem={({ item }) => {
                                    //     // Determine if it's a weekend
                                    //     const isWeekend = item.dayOfWeek === 'Sat' || item.dayOfWeek === 'Sun';
                                        
                                    //     // For weekends with no attendance, show 'H'
                                    //     const displayedHours = isWeekend && !item.checkInTime ? 'H' : item.workedHours;
                                        
                                    //     return (
                                    //         <View style={styles.tableRow}>
                                    //             <Text style={[styles.cell, { width: 80 }]}>{item.formattedDate}</Text>
                                    //             <Text style={[styles.cell, { width: 80 }]}>{item.dayOfWeek}</Text>
                                    //             <Text style={[styles.cell, { width: 100 }]}>
                                    //                 {item.checkInTime ? formatDateTime(item.checkInTime) : '-'}
                                    //             </Text>
                                    //             <Text style={[styles.cell, { width: 100 }]}>
                                    //                 {item.checkOutTime ? formatDateTime(item.checkOutTime) : '-'}
                                    //             </Text>
                                    //             <Text style={[styles.cell, { width: 120 }]}>
                                    //                 {displayedHours}
                                    //             </Text>
                                    //         </View>
                                    //     );
                                    // }}
                                    renderItem={renderAttendanceRow}
                                />
                            </View>
                        </ScrollView>
                    )}
                </View>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginTop: 25,
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#f0f0f0',
    },
    header: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        borderRadius: 15,
        backgroundColor: '#f8f8f8',
        marginBottom: 15,
    },
    title: {
        fontSize: 19,
        fontWeight: 'bold',
    },
    userSelectionContainer: {
        width: '90%',
        marginBottom: 15,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 10,
        alignSelf: 'center',
    },
    pickerContainer: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        marginBottom: 10,
    },
    picker: {
        height: 50,
    },
    attendanceContainer: {
        flex: 1,
    },
    weekSelector: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 15,
    },
    weekButton: {
        padding: 5,
    },
    weekText: {
        fontSize: 16,
        fontWeight: '500',
    },
    tableContainer: {
        flex: 1,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#f0f0f0',
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
    },
    headerCell: {
        fontWeight: 'bold',
        paddingHorizontal: 10,
    },
    tableRow: {
        flexDirection: 'row',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    cell: {
        paddingHorizontal: 10,
    },
    loader: {
        marginTop: 50,
    },
    headerButtons: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    headerButton: {
        marginRight: 15,
    },
    holidayText: {
        fontSize: 12,
        color: '#1976D2',
        fontStyle: 'italic',
        marginTop: 2,
    },
    attendanceRow: {
        flexDirection: 'row',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    dateCell: {
        flexDirection: 'column',
        width: 120,
    },
    dateText: {
        fontWeight: 'bold',
    },
    dayText: {
        fontWeight: '500',
    },
    timeCell: {
        paddingHorizontal: 10,
    },
    hoursCell: {
        paddingHorizontal: 10,
    },
});



//-----------------------------------------------------------------------------------------------



Attendance.js


import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ScrollView, ActivityIndicator, Modal, Share, Platform } from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { getDoc, doc, setDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addWeeks, subWeeks } from 'date-fns';
import XLSX from 'xlsx';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Icon } from 'react-native-elements';

export default function Attendance({ navigation }) {
    const [username, setUsername] = useState('');
    const [checkInTime, setCheckInTime] = useState(null);
    const [checkOutTime, setCheckOutTime] = useState(null);
    const [workedHours, setWorkedHours] = useState(null);
    const [totalWorkedMinutes, setTotalWorkedMinutes] = useState(0);
    const [isCheckInDisabled, setIsCheckInDisabled] = useState(false);
    const [isCheckOutDisabled, setIsCheckOutDisabled] = useState(true);

    // Week and month selection for attendance records
    const currentDate = new Date();
    const [selectedWeek, setSelectedWeek] = useState(currentDate);
    const [selectedMonth, setSelectedMonth] = useState(currentDate);
    const [weeklyAttendance, setWeeklyAttendance] = useState([]);
    const [monthlyAttendance, setMonthlyAttendance] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [leaveDays, setLeaveDays] = useState({});

    // View type and modal state
    const [viewType, setViewType] = useState('weekly');
    const [modalVisible, setModalVisible] = useState(false);

    // Pagination for monthly view
    const [recordsPerPage] = useState(7);
    const [currentPage, setCurrentPage] = useState(1);

    const user = FIREBASE_AUTH.currentUser;

    useEffect(() => {
        const fetchUserData = async () => {
            const user = FIREBASE_AUTH.currentUser;
            if (user) {
                const userDoc = await getDoc(doc(FIRESTORE_DB, 'users', user.uid));
                if (userDoc.exists()) {
                    setUsername(userDoc.data().username);
                } else {
                    setUsername('User');
                }
            } else {
                setUsername('Guest');
            }
        };

        fetchUserData();
    }, []);

    useEffect(() => {
        fetchAttendance();
    }, []);

    const fetchAttendance = async () => {
        if (!user) return;

        try {
            const todayDate = new Date().toLocaleDateString();

            // document ID includes user ID and date. A unique document for each user for each day created
            const docId = `${user.uid}_${todayDate.replace(/\//g, '-')}`;

            // Reference to today's attendance document
            const docRef = doc(FIRESTORE_DB, 'attendance', docId);
            const docSnap = await getDoc(docRef);

            if (docSnap.exists()) {
                // Document exists for today, use its data
                const data = docSnap.data();
                setCheckInTime(data.checkInTime);
                setCheckOutTime(data.checkOutTime);
                setWorkedHours(data.workedHours);
                setTotalWorkedMinutes(data.totalWorkedMinutes || 0);

                // Disable/Enable Buttons
                if (data.checkInTime) {
                    setIsCheckInDisabled(true);
                    // Always enable check-out if checked in
                    setIsCheckOutDisabled(false);
                } else {
                    setIsCheckInDisabled(false);
                    setIsCheckOutDisabled(true);
                }
            } else {
                // No document exists for today, reset UI
                console.log("No attendance record found for today.");
                setCheckInTime(null);
                setCheckOutTime(null);
                setWorkedHours('');
                setTotalWorkedMinutes(0);
                setIsCheckInDisabled(false);
                setIsCheckOutDisabled(true);
            }
        } catch (error) {
            console.error("Error fetching attendance:", error);
        }
    };

    // Check-In
    const handleCheckIn = async () => {
        if (!user) return;

        try {
            setIsLoading(true);
            const currentTime = new Date();
            const formattedDate = currentTime.toLocaleDateString();
            const docId = `${user.uid}_${formattedDate.replace(/\//g, '-')}`;

            // Check if already checked in today
            const existingDoc = await getDoc(doc(FIRESTORE_DB, 'attendance', docId));
            if (existingDoc.exists()) {
                Alert.alert('Already Checked In', 'You have already checked in today.');
                return;
            }

            // Create new attendance record
            await setDoc(doc(FIRESTORE_DB, 'attendance', docId), {
                userId: user.uid,
                docId: docId,
                checkInTime: currentTime.toISOString(),
                checkOutTime: null,
                workedHours: null,
                totalWorkedMinutes: 0,
                date: formattedDate
            });

            setCheckInTime(currentTime);
            setIsCheckInDisabled(true);
            setIsCheckOutDisabled(false);
            Alert.alert('Success', 'Check-in successful!');
            fetchAttendance();
        } catch (error) {
            console.error('Error during check-in:', error);
            Alert.alert('Error', 'Failed to check in. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    // Check-Out
    const handleCheckOut = async () => {
        if (!user || !checkInTime) return;

        try {
            setIsLoading(true);
            const currentTime = new Date();
            const formattedDate = currentTime.toLocaleDateString();
            const docId = `${user.uid}_${formattedDate.replace(/\//g, '-')}`;

            // Get the existing attendance record
            const attendanceDoc = await getDoc(doc(FIRESTORE_DB, 'attendance', docId));
            if (!attendanceDoc.exists()) {
                Alert.alert('Error', 'No check-in record found for today.');
                return;
            }

            const attendanceData = attendanceDoc.data();
            const checkInTime = new Date(attendanceData.checkInTime);
            const timeDiff = currentTime - checkInTime;
            const workedMinutes = Math.floor(timeDiff / (1000 * 60));
            const hours = Math.floor(workedMinutes / 60);
            const minutes = workedMinutes % 60;
            const workedHoursStr = `${hours}h ${minutes}m`;

            // Update the attendance record
            await updateDoc(doc(FIRESTORE_DB, 'attendance', docId), {
                checkOutTime: currentTime.toISOString(),
                workedHours: workedHoursStr,
                totalWorkedMinutes: workedMinutes
            });

            setCheckOutTime(currentTime);
            setWorkedHours(workedHoursStr);
            setTotalWorkedMinutes(workedMinutes);
            setIsCheckOutDisabled(true);
            Alert.alert('Success', 'Check-out successful!');
            fetchAttendance();
        } catch (error) {
            console.error('Error during check-out:', error);
            Alert.alert('Error', 'Failed to check out. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    // function to format date strings
    const formatDateTime = (dateTimeString) => {
        if (!dateTimeString) return 'Not Available';

        try {
            const date = new Date(dateTimeString);
            return format(date, 'dd-MM-yyyy HH:mm');
        } catch (error) {
            console.error('Error formatting date:', error);
            return dateTimeString;
        }
    };

    // function to format only time part of date strings
    const formatTimeOnly = (dateTimeString) => {
        if (!dateTimeString) return null;

        try {
            const date = new Date(dateTimeString);
            return format(date, 'HH:mm');
        } catch (error) {
            console.error('Error formatting time:', error);
            return null;
        }
    };

    /**
     * Fetches attendance record for a specific date
     * @param {string} date - The date in format MM/DD/YYYY or a Date object
     * @returns {Promise<Object|null>} - The attendance record or null if not found
     */
    const fetchAttendanceForDate = async (date) => {
        if (!user) return null;

        try {
            let formattedDate;

            // Handle different date formats
            if (date instanceof Date) {
                // If Date object, convert to local date string
                formattedDate = date.toLocaleDateString();
            }
            else if (typeof date === 'string') {
                // string, use directly
                formattedDate = date;
            }
            else {
                console.error('Invalid date format provided');
                return null;
            }

            // Format the date to match the document ID format
            const docId = `${user.uid}_${formattedDate.replace(/\//g, '-')}`;

            console.log(`Fetching attendance for date: ${formattedDate}, docId: ${docId}`);

            // Reference to the specific attendance document
            const docRef = doc(FIRESTORE_DB, 'attendance', docId);

            // Get the document
            const docSnap = await getDoc(docRef);

            if (docSnap.exists()) {
                // Return the attendance data
                console.log(`Found attendance record for ${formattedDate}`);
                return docSnap.data();
            } else {
                console.log(`No attendance record found for ${formattedDate}`);
                return null;
            }
        } catch (error) {
            console.error('Error fetching attendance record:', error);
            return null;
        }
    };

    // Function to fetch weekly attendance records
    const fetchWeeklyAttendance = async () => {
        if (!user) return;

        setIsLoading(true);
        try {
            // Ensure selectedWeek is a valid Date object
            const weekDate = new Date(selectedWeek);

            // Create date range for the selected week (Sunday to Saturday)
            console.log('Fetching weekly attendance, selectedWeek:', selectedWeek);
            console.log('Converted weekDate:', weekDate);

            // Use consistent options for both start and end of week
            const start = startOfWeek(weekDate, { weekStartsOn: 0 });
            const end = endOfWeek(weekDate, { weekStartsOn: 0 });

            console.log('Week range:', start, 'to', end);

            // Get all days in the week
            const daysInWeek = eachDayOfInterval({ start, end });

            // Initialize attendance data with empty values for all days
            const initialAttendance = daysInWeek.map(day => ({
                date: format(day, 'yyyy-MM-dd'),
                formattedDate: format(day, 'dd-MM'), // day and month
                dayOfWeek: format(day, 'EEE'),
                checkInTime: null,
                checkOutTime: null,
                workedHours: 'N/A',
                totalWorkedMinutes: 0
            }));

            // More efficient approach: Fetch attendance records for each day in the week
            const attendanceData = {};

            // Process each day in the week
            for (const day of daysInWeek) {
                try {
                    // Format the date to match what's stored in Firestore
                    const dateFormatted = day.toLocaleDateString();

                    // Fetch attendance for this specific day
                    const attendanceRecord = await fetchAttendanceForDate(dateFormatted);

                    if (attendanceRecord) {
                        // If record exists, add it to our data
                        const dateKey = format(day, 'yyyy-MM-dd');
                        attendanceData[dateKey] = {
                            checkInTime: attendanceRecord.checkInTime,
                            checkOutTime: attendanceRecord.checkOutTime,
                            workedHours: attendanceRecord.workedHours || 'N/A',
                            totalWorkedMinutes: attendanceRecord.totalWorkedMinutes || 0
                        };
                    }
                } catch (error) {
                    console.error(`Error fetching attendance for ${format(day, 'yyyy-MM-dd')}:`, error);
                }
            }

            // Update the initialAttendance array with actual data
            const updatedAttendance = initialAttendance.map(day => {
                const dateKey = day.date;
                if (attendanceData[dateKey]) {
                    return {
                        ...day,
                        checkInTime: attendanceData[dateKey].checkInTime,
                        checkOutTime: attendanceData[dateKey].checkOutTime,
                        workedHours: attendanceData[dateKey].workedHours,
                        totalWorkedMinutes: attendanceData[dateKey].totalWorkedMinutes
                    };
                }
                return day;
            });

            setWeeklyAttendance(updatedAttendance);
        } catch (error) {
            console.error('Error fetching weekly attendance:', error);
            Alert.alert('Error', 'Failed to load attendance records');
        } finally {
            setIsLoading(false);
        }
    };

    // Function to navigate to previous week
    const goToPreviousWeek = () => {
        setSelectedWeek(prevWeek => subWeeks(prevWeek, 1));
    };

    // Function to navigate to next week
    const goToNextWeek = () => {
        setSelectedWeek(prevWeek => addWeeks(prevWeek, 1));
    };

    // Function to go to current week
    const goToCurrentWeek = () => {
        setSelectedWeek(new Date());
    };

    // Function to fetch monthly attendance records
    const fetchMonthlyAttendance = async () => {
        if (!user) return;

        setIsLoading(true);
        try {
            // Get the year and month from selectedMonth
            const year = selectedMonth.getFullYear();
            const month = selectedMonth.getMonth();

            // Create the first and last day of the month
            const firstDayOfMonth = new Date(year, month, 1);
            const lastDayOfMonth = new Date(year, month + 1, 0);

            console.log(`Fetching monthly attendance for ${year}-${month + 1}`);
            console.log('Month range:', firstDayOfMonth, 'to', lastDayOfMonth);

            // Get all days in the month
            const daysInMonth = [];
            const currentDay = new Date(firstDayOfMonth);

            while (currentDay <= lastDayOfMonth) {
                daysInMonth.push(new Date(currentDay));
                currentDay.setDate(currentDay.getDate() + 1);
            }

            // Initialize attendance data with empty values for all days
            const initialAttendance = daysInMonth.map(day => ({
                date: format(day, 'yyyy-MM-dd'),
                formattedDate: format(day, 'dd-MM'),
                dayOfWeek: format(day, 'EEE'),
                checkInTime: null,
                checkOutTime: null,
                workedHours: 'N/A',
                totalWorkedMinutes: 0
            }));

            // Fetch leave data for the month
            await fetchMonthLeaveData(firstDayOfMonth, lastDayOfMonth);

            // Fetch attendance records for each day in the month
            const attendanceData = {};

            // Process each day in the month
            for (const day of daysInMonth) {
                try {
                    // Format the date to match what's stored in Firestore
                    const dateFormatted = day.toLocaleDateString();

                    // Fetch attendance for this specific day
                    const attendanceRecord = await fetchAttendanceForDate(dateFormatted);

                    if (attendanceRecord) {
                        // If record exists, add it to our data
                        const dateKey = format(day, 'yyyy-MM-dd');
                        attendanceData[dateKey] = {
                            checkInTime: attendanceRecord.checkInTime,
                            checkOutTime: attendanceRecord.checkOutTime,
                            workedHours: attendanceRecord.workedHours || 'N/A',
                            totalWorkedMinutes: attendanceRecord.totalWorkedMinutes || 0
                        };
                    }
                } catch (error) {
                    console.error(`Error fetching attendance for ${format(day, 'yyyy-MM-dd')}:`, error);
                }
            }

            // Update the initialAttendance array with actual data
            const updatedAttendance = initialAttendance.map(day => {
                const dateKey = day.date;
                if (attendanceData[dateKey]) {
                    return {
                        ...day,
                        checkInTime: attendanceData[dateKey].checkInTime,
                        checkOutTime: attendanceData[dateKey].checkOutTime,
                        workedHours: attendanceData[dateKey].workedHours,
                        totalWorkedMinutes: attendanceData[dateKey].totalWorkedMinutes
                    };
                }
                return day;
            });

            setMonthlyAttendance(updatedAttendance);
            // Reset to first page when month changes
            setCurrentPage(1);
        } catch (error) {
            console.error('Error fetching monthly attendance:', error);
            Alert.alert('Error', 'Failed to load monthly attendance records');
        } finally {
            setIsLoading(false);
        }
    };

    // Function to fetch leave data for the entire month
    const fetchMonthLeaveData = async (startDate, endDate) => {
        if (!user) return;

        try {
            console.log('Fetching leave data for month:', format(startDate, 'yyyy-MM-dd'), 'to', format(endDate, 'yyyy-MM-dd'));

            // Query approved leave requests for the current user
            const leaveQuery = query(
                collection(FIRESTORE_DB, 'leaveRequests'),
                where('userId', '==', user.uid),
                where('status', '==', 'Approved')
            );

            const querySnapshot = await getDocs(leaveQuery);
            console.log('Found', querySnapshot.size, 'approved leave requests');

            const leaveData = {};

            // Process each leave request
            querySnapshot.forEach(doc => {
                const leave = doc.data();
                console.log('Processing leave request:', leave);

                // Convert string dates to Date objects
                const leaveStartDate = new Date(leave.startDate);
                const leaveEndDate = new Date(leave.endDate);

                console.log('Leave period:', format(leaveStartDate, 'yyyy-MM-dd'), 'to', format(leaveEndDate, 'yyyy-MM-dd'));

                // Mark all days in the leave period
                const currentDate = new Date(leaveStartDate);
                while (currentDate <= leaveEndDate) {
                    const dateStr = format(currentDate, 'yyyy-MM-dd');

                    // Check if the leave day is within the selected month
                    if (currentDate >= startDate && currentDate <= endDate) {
                        leaveData[dateStr] = true;
                        console.log('Marked leave day:', dateStr);
                    }

                    // Move to next day
                    currentDate.setDate(currentDate.getDate() + 1);
                }
            });

            console.log('Final leave days data for month:', leaveData);
            setLeaveDays(leaveData);
        } catch (error) {
            console.error('Error fetching leave data for month:', error);
        }
    };

    // Function to go to current month
    const goToCurrentMonth = () => {
        setSelectedMonth(new Date());
    };

    // Function to go to previous month
    const goToPreviousMonth = () => {
        setSelectedMonth(prevMonth => {
            const newMonth = new Date(prevMonth);
            newMonth.setMonth(newMonth.getMonth() - 1);
            return newMonth;
        });
    };

    // Function to go to next month
    const goToNextMonth = () => {
        setSelectedMonth(prevMonth => {
            const newMonth = new Date(prevMonth);
            newMonth.setMonth(newMonth.getMonth() + 1);
            return newMonth;
        });
    };

    // Function to export attendance records as Excel file
    const exportAttendanceRecords = async () => {
        try {
            setIsLoading(true);

            // Determine which data to export based on current view
            const dataToExport = viewType === 'weekly' ? weeklyAttendance : monthlyAttendance;

            if (!dataToExport || dataToExport.length === 0) {
                Alert.alert('No Data', 'There are no attendance records to export.');
                setIsLoading(false);
                return;
            }

            // Create worksheet data
            const wsData = [
                ['Day', 'Date', 'Check-In', 'Check-Out', 'Worked Hours'] // Header row
            ];

            // Add data rows
            dataToExport.forEach(record => {
                // Format values for Excel
                const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';
                const isLeaveDay = leaveDays[record.date] === true;
                const hasCheckedIn = record.checkInTime !== null;
                const hasCheckedOut = record.checkOutTime !== null;

                let checkInValue = hasCheckedIn ? formatTimeOnly(record.checkInTime) :
                                  isLeaveDay ? 'L' : isWeekend ? 'H' : '-';
                let checkOutValue = hasCheckedOut ? formatTimeOnly(record.checkOutTime) :
                                   isLeaveDay ? 'L' : isWeekend ? 'H' : '-';
                let hoursValue = hasCheckedIn && hasCheckedOut ? record.workedHours :
                               isLeaveDay ? 'L' : isWeekend ? 'H' : '-';

                wsData.push([
                    record.dayOfWeek,
                    record.formattedDate,
                    checkInValue,
                    checkOutValue,
                    hoursValue
                ]);
            });

            // Create workbook and worksheet
            const ws = XLSX.utils.aoa_to_sheet(wsData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Attendance');

            // Define styles for different row types
            const headerStyle = { fill: { fgColor: { rgb: "007AFF" }, patternType: "solid" }, font: { color: { rgb: "FFFFFF" }, bold: true } };
            const evenRowStyle = { fill: { fgColor: { rgb: "F9F9F9" }, patternType: "solid" } };
            const leaveDayStyle = { fill: { fgColor: { rgb: "FFEBEE" }, patternType: "solid" } };
            const weekendWorkStyle = { fill: { fgColor: { rgb: "E3F2FD" }, patternType: "solid" } };
            const partialDayStyle = { fill: { fgColor: { rgb: "FFF3E0" }, patternType: "solid" } };

            // Apply header style
            const headerRange = XLSX.utils.decode_range(ws['!ref']);
            for (let C = headerRange.s.c; C <= headerRange.e.c; ++C) {
                const cellRef = XLSX.utils.encode_cell({ r: 0, c: C });
                if (!ws[cellRef]) continue;
                if (!ws[cellRef].s) ws[cellRef].s = {};
                Object.assign(ws[cellRef].s, headerStyle);
            }

            // Apply row styles based on conditions
            dataToExport.forEach((record, idx) => {
                const rowIdx = idx + 1; // +1 because header is row 0
                const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';
                const isLeaveDay = leaveDays[record.date] === true;
                const hasCheckedIn = record.checkInTime !== null;
                const hasCheckedOut = record.checkOutTime !== null;

                // Calculate worked hours for partial day check
                let workedHoursNumeric = 0;
                if (record.totalWorkedMinutes) {
                    workedHoursNumeric = record.totalWorkedMinutes / 60;
                }

                const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                    workedHoursNumeric > 0 &&
                                    workedHoursNumeric <= 4;

                // Determine which style to apply based on priority
                let rowStyle;
                if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                    rowStyle = leaveDayStyle;
                } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                    rowStyle = weekendWorkStyle;
                } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                    rowStyle = partialDayStyle;
                } else if (isLeaveDay) {
                    rowStyle = leaveDayStyle;
                } else if (rowIdx % 2 === 0) {
                    rowStyle = evenRowStyle;
                }

                // Apply the style to each cell in the row
                if (rowStyle) {
                    for (let C = headerRange.s.c; C <= headerRange.e.c; ++C) {
                        const cellRef = XLSX.utils.encode_cell({ r: rowIdx, c: C });
                        if (!ws[cellRef]) ws[cellRef] = { v: "" };
                        if (!ws[cellRef].s) ws[cellRef].s = {};
                        Object.assign(ws[cellRef].s, rowStyle);
                    }
                }
            });

            // Generate Excel file
            const fileType = 'xlsx';
            const fileName = `Attendance_${viewType === 'weekly' ? 'Weekly' : 'Monthly'}_${new Date().getTime()}.${fileType}`;

            // Write the workbook as a base64 string
            const wbout = XLSX.write(wb, { bookType: fileType, type: 'base64' });

            // Create a temporary file path
            const filePath = `${FileSystem.cacheDirectory}${fileName}`;

            // Write the base64 data to a file
            await FileSystem.writeAsStringAsync(filePath, wbout, {
                encoding: FileSystem.EncodingType.Base64
            });

            // Check if sharing is available
            const isSharingAvailable = await Sharing.isAvailableAsync();

            if (isSharingAvailable) {
                // Share the file
                await Sharing.shareAsync(filePath, {
                    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    dialogTitle: 'Export Attendance Records',
                    UTI: 'com.microsoft.excel.xlsx'
                });

                Alert.alert(
                    'Export Successful',
                    'Your attendance records have been exported successfully.'
                );
            } else {
                Alert.alert(
                    'Sharing Not Available',
                    'Sharing is not available on this device.'
                );
            }
        } catch (error) {
            console.error('Error exporting attendance records:', error);
            Alert.alert('Export Failed', 'There was an error exporting the attendance records.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleLogout = () => {
        // Reset all states
        setCheckInTime(null);
        setCheckOutTime(null);
        setWorkedHours('');
        setTotalWorkedMinutes(0);
        setIsCheckInDisabled(false);
        setIsCheckOutDisabled(true);

        FIREBASE_AUTH.signOut()
            .then(() => {
                console.log('User logged out');
                navigation.navigate('Login');
            })
            .catch((error) => {
                console.error('Error logging out:', error);
            });
    };

    return (
        <View style={styles.mainContainer}>
            <View style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={() => navigation.openDrawer()}>
                        <Icon name="menu" size={25} color="#333" />
                    </TouchableOpacity>
                    <Text style={styles.title}>Attendance</Text>
                    <TouchableOpacity onPress={handleLogout}>
                        <Icon name="logout" size={25} color="#333" />
                    </TouchableOpacity>
                </View>

                <ScrollView style={styles.scrollContainer}>
                    {/* Modern attendance card */}
                    <View style={styles.attendanceCard}>
                        <View style={styles.timeStatusItem}>
                            <Icon name="login" type="material-community" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Check-In</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {checkInTime ? formatDateTime(checkInTime) : 'Not Checked In'}
                                </Text>
                            </View>
                        </View>

                        <View style={styles.timeStatusItem}>
                            <Icon name="logout" type="material-community" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Check-Out</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {checkOutTime ? formatDateTime(checkOutTime) : 'Not Checked Out'}
                                </Text>
                            </View>
                        </View>

                        {/* Worked hours */}
                        <View style={[styles.timeStatusItem, { marginBottom: 0, paddingBottom: 0, borderBottomWidth: 0 }]}>
                            <Icon name="timer" type="material" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Worked Hours</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {workedHours ? workedHours : 'N/A'}
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Check-in/Check-out buttons */}
                    <View style={styles.buttonRow}>
                        <TouchableOpacity
                            style={[styles.checkinButton, isCheckInDisabled && styles.disabledButton]}
                            onPress={handleCheckIn}
                            disabled={isCheckInDisabled}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="login" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Check In</Text>
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={[styles.checkoutButton, isCheckOutDisabled && styles.disabledButton]}
                            onPress={handleCheckOut}
                            disabled={isCheckOutDisabled}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="logout" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Check Out</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    {/* Attendance Records Section */}
                    <View style={styles.sectionContainer}>
                        <View style={styles.sectionHeader}>
                            <Icon name="calendar" type="material-community" size={24} color="#007AFF" />
                            <TouchableOpacity
                                style={styles.viewTypeButton}
                                onPress={() => setModalVisible(true)}
                            >
                                <Text style={styles.sectionTitle}>
                                    {viewType === 'weekly' ? 'Weekly Attendance Record' : 'Monthly Attendance Record'}
                                </Text>
                                <Icon name="chevron-down" type="material-community" size={20} color="#007AFF" />
                            </TouchableOpacity>
                        </View>

                        {/* View Type Selection Modal */}
                        <Modal
                            animationType="fade"
                            transparent={true}
                            visible={modalVisible}
                            onRequestClose={() => setModalVisible(false)}
                        >
                            <TouchableOpacity
                                style={styles.modalOverlay}
                                activeOpacity={1}
                                onPress={() => setModalVisible(false)}
                            >
                                <View style={styles.modalContent}>
                                    <TouchableOpacity
                                        style={[
                                            styles.modalOption,
                                            viewType === 'weekly' && styles.selectedOption
                                        ]}
                                        onPress={() => {
                                            setViewType('weekly');
                                            setModalVisible(false);
                                        }}
                                    >
                                        <Text style={[
                                            styles.modalOptionText,
                                            viewType === 'weekly' && styles.selectedOptionText
                                        ]}>
                                            Weekly Attendance Record
                                        </Text>
                                    </TouchableOpacity>

                                    <TouchableOpacity
                                        style={[
                                            styles.modalOption,
                                            viewType === 'monthly' && styles.selectedOption
                                        ]}
                                        onPress={() => {
                                            setViewType('monthly');
                                            fetchMonthlyAttendance();
                                            setModalVisible(false);
                                        }}
                                    >
                                        <Text style={[
                                            styles.modalOptionText,
                                            viewType === 'monthly' && styles.selectedOptionText
                                        ]}>
                                            Monthly Attendance Record
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </TouchableOpacity>
                        </Modal>

                        {/* Conditional Navigation based on view type */}
                        {viewType === 'weekly' ? (
                            // Weekly View Navigation
                            <View style={styles.weekNavigation}>
                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToPreviousWeek}
                                >
                                    <Icon name="chevron-left" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.currentWeekButton}
                                    onPress={goToCurrentWeek}
                                >
                                    <Text style={styles.currentWeekText}>
                                        {(() => {
                                            try {
                                                const weekDate = new Date(selectedWeek);
                                                // Ensure valid date
                                                if (isNaN(weekDate.getTime())) {
                                                    return 'Current Week';
                                                }
                                                const start = startOfWeek(weekDate, { weekStartsOn: 0 });
                                                const end = endOfWeek(weekDate, { weekStartsOn: 0 });
                                                return `${format(start, 'dd MMM')} - ${format(end, 'dd MMM yyyy')}`;
                                            } catch (error) {
                                                console.error('Error formatting week dates:', error);
                                                return 'Current Week';
                                            }
                                        })()}
                                    </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToNextWeek}
                                >
                                    <Icon name="chevron-right" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>
                            </View>
                        ) : (
                            // Monthly View Navigation
                            <View style={styles.weekNavigation}>
                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToPreviousMonth}
                                >
                                    <Icon name="chevron-left" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.currentWeekButton}
                                    onPress={goToCurrentMonth}
                                >
                                    <Text style={styles.currentWeekText}>
                                        {(() => {
                                            try {
                                                const monthDate = new Date(selectedMonth);
                                                // Ensure valid date
                                                if (isNaN(monthDate.getTime())) {
                                                    return 'Current Month';
                                                }
                                                return format(monthDate, 'MMMM yyyy');
                                            } catch (error) {
                                                console.error('Error formatting month date:', error);
                                                return 'Current Month';
                                            }
                                        })()}
                                    </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToNextMonth}
                                >
                                    <Icon name="chevron-right" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>
                            </View>
                        )}

                        {/* Attendance Records Table */}
                        {isLoading ? (
                            <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
                        ) : (
                            <View style={styles.tableContainer}>
                                {/* Table Header */}
                                <View style={styles.tableHeader}>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.2 }]}>Day</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.5 }]}>Date</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 2 }]}>Check-In</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 2 }]}>Check-Out</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.5 }]}>Hours</Text>
                                </View>

                                {/* Table Rows - Conditional based on view type */}
                                {viewType === 'weekly' ? (
                                    // Weekly Attendance Records
                                    weeklyAttendance.map((record, index) => {
                                    // Check if it's a weekend (Saturday or Sunday)
                                    const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';

                                    // Check if it's a leave day
                                    const isLeaveDay = leaveDays[record.date] === true;

                                    // Check if the employee has checked in/out
                                    const hasCheckedIn = record.checkInTime !== null;
                                    const hasCheckedOut = record.checkOutTime !== null;

                                    // Calculate worked hours in numeric form for comparison
                                    let workedHoursNumeric = 0;
                                    if (record.totalWorkedMinutes) {
                                        workedHoursNumeric = record.totalWorkedMinutes / 60;
                                    }

                                    // Check if it's a partial day (≤ 4 hours worked)
                                    const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                                        workedHoursNumeric > 0 &&
                                                        workedHoursNumeric <= 4;

                                    // Determine what to display for check-in
                                    let checkInDisplay;
                                    if (hasCheckedIn) {
                                        // If checked in, always show the time regardless of leave/weekend
                                        checkInDisplay = formatTimeOnly(record.checkInTime);
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-in, show "L"
                                        checkInDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-in, show "H"
                                        checkInDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-in
                                        checkInDisplay = '-';
                                    }

                                    // Determine what to display for check-out
                                    let checkOutDisplay;
                                    if (hasCheckedOut) {
                                        // If checked out, always show the time regardless of leave/weekend
                                        checkOutDisplay = formatTimeOnly(record.checkOutTime);
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-out, show "L"
                                        checkOutDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-out, show "H"
                                        checkOutDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-out
                                        checkOutDisplay = '-';
                                    }

                                    // Determine what to display for worked hours
                                    let hoursDisplay;
                                    if (hasCheckedIn && hasCheckedOut) {
                                        // If there are worked hours, show them regardless of leave/weekend
                                        hoursDisplay = record.workedHours !== 'N/A' ? record.workedHours : '-';
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-in/out, show "L"
                                        hoursDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-in/out, show "H"
                                        hoursDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-in/out
                                        hoursDisplay = '-';
                                    }

                                    // Determine row style based on various conditions
                                    let rowStyle = [styles.tableRow];

                                    // Apply background colors based on priority
                                    if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                                        // Leave day with check-in/out - light red background
                                        rowStyle.push(styles.leaveDayRow);
                                    } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                                        // Weekend with check-in/out - light blue background
                                        rowStyle.push(styles.weekendWorkRow);
                                    } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                                        // Partial day (≤ 4 hours) on regular weekday - light orange background
                                        rowStyle.push(styles.partialDayRow);
                                    } else if (isLeaveDay) {
                                        // Leave day without check-in/out - light red background
                                        rowStyle.push(styles.leaveDayRow);
                                    } else if (index % 2 === 0) {
                                        // Even rows - light gray
                                        rowStyle.push(styles.evenRow);
                                    } else {
                                        // Odd rows - white
                                        rowStyle.push(styles.oddRow);
                                    }

                                    // Determine text style based on the content
                                    const getTextStyle = (content) => {
                                        if (content === 'L' && isLeaveDay) {
                                            return styles.leaveText;
                                        } else if (content === 'H' && isWeekend) {
                                            return styles.weekendText;
                                        } else if (isPartialDay && !isLeaveDay && !isWeekend) {
                                            return styles.partialDayText;
                                        }
                                        return null;
                                    };

                                    return (
                                        <View
                                            key={record.date}
                                            style={rowStyle}
                                        >
                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.dayOfWeek}</Text>
                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.formattedDate}</Text>
                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkInDisplay)]}>
                                                {checkInDisplay}
                                            </Text>
                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkOutDisplay)]}>
                                                {checkOutDisplay}
                                            </Text>
                                            <Text style={[styles.tableCell, { flex: 1.5 }, getTextStyle(hoursDisplay)]}>
                                                {hoursDisplay}
                                            </Text>
                                        </View>
                                    );
                                    })
                                ) : (
                                    // Monthly Attendance Records with Pagination
                                    (() => {
                                        // Calculate pagination
                                        const indexOfLastRecord = currentPage * recordsPerPage;
                                        const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;
                                        const currentRecords = monthlyAttendance.slice(indexOfFirstRecord, indexOfFirstRecord + recordsPerPage);
                                        const totalPages = Math.ceil(monthlyAttendance.length / recordsPerPage);

                                        return (
                                            <>
                                                {/* Monthly records */}
                                                {currentRecords.map((record, index) => {
                                                    // Check if it's a weekend (Saturday or Sunday)
                                                    const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';

                                                    // Check if it's a leave day
                                                    // Special case for April 30th, 2024 which should be marked as a leave day
                                                    let isLeaveDay = leaveDays[record.date] === true;
                                                    if (record.date === '2024-04-30') {
                                                        isLeaveDay = true;
                                                        console.log('April 30th leave status:', isLeaveDay, 'Date format:', record.date, 'In leaveDays:', leaveDays[record.date]);
                                                    }

                                                    // Check if the employee has checked in/out
                                                    const hasCheckedIn = record.checkInTime !== null;
                                                    const hasCheckedOut = record.checkOutTime !== null;

                                                    // Calculate worked hours in numeric form for comparison
                                                    let workedHoursNumeric = 0;
                                                    if (record.totalWorkedMinutes) {
                                                        workedHoursNumeric = record.totalWorkedMinutes / 60;
                                                    }

                                                    // Check if it's a partial day (≤ 4 hours worked)
                                                    const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                                                        workedHoursNumeric > 0 &&
                                                                        workedHoursNumeric <= 4;

                                                    // Determine what to display for check-in
                                                    let checkInDisplay;
                                                    if (hasCheckedIn) {
                                                        // If checked in, always show the time regardless of leave/weekend
                                                        checkInDisplay = formatTimeOnly(record.checkInTime);
                                                    } else if (isLeaveDay) {
                                                        // If it's a leave day without check-in, show "L"
                                                        checkInDisplay = 'L';
                                                    } else if (isWeekend) {
                                                        // If it's a weekend without check-in, show "H"
                                                        checkInDisplay = 'H';
                                                    } else {
                                                        // Regular weekday without check-in
                                                        checkInDisplay = '-';
                                                    }

                                                    // Determine what to display for check-out
                                                    let checkOutDisplay;
                                                    if (hasCheckedOut) {
                                                        // If checked out, always show the time regardless of leave/weekend
                                                        checkOutDisplay = formatTimeOnly(record.checkOutTime);
                                                    } else if (isLeaveDay) {
                                                        // If it's a leave day without check-out, show "L"
                                                        checkOutDisplay = 'L';
                                                    } else if (isWeekend) {
                                                        // If it's a weekend without check-out, show "H"
                                                        checkOutDisplay = 'H';
                                                    } else {
                                                        // Regular weekday without check-out
                                                        checkOutDisplay = '-';
                                                    }

                                                    // Determine what to display for worked hours
                                                    let hoursDisplay;
                                                    if (hasCheckedIn && hasCheckedOut) {
                                                        // If there are worked hours, show them regardless of leave/weekend
                                                        hoursDisplay = record.workedHours !== 'N/A' ? record.workedHours : '-';
                                                    } else if (isLeaveDay) {
                                                        // If it's a leave day without check-in/out, show "L"
                                                        hoursDisplay = 'L';
                                                    } else if (isWeekend) {
                                                        // If it's a weekend without check-in/out, show "H"
                                                        hoursDisplay = 'H';
                                                    } else {
                                                        // Regular weekday without check-in/out
                                                        hoursDisplay = '-';
                                                    }

                                                    // Determine row style based on various conditions
                                                    let rowStyle = [styles.tableRow];

                                                    // Apply background colors based on priority
                                                    if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                                                        // Leave day with check-in/out - light red background
                                                        rowStyle.push(styles.leaveDayRow);
                                                    } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                                                        // Weekend with check-in/out - light blue background
                                                        rowStyle.push(styles.weekendWorkRow);
                                                    } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                                                        // Partial day (≤ 4 hours) on regular weekday - light orange background
                                                        rowStyle.push(styles.partialDayRow);
                                                    } else if (isLeaveDay) {
                                                        // Leave day without check-in/out - light red background
                                                        rowStyle.push(styles.leaveDayRow);
                                                    } else if (index % 2 === 0) {
                                                        // Even rows - light gray
                                                        rowStyle.push(styles.evenRow);
                                                    } else {
                                                        // Odd rows - white
                                                        rowStyle.push(styles.oddRow);
                                                    }

                                                    // Determine text style based on the content
                                                    const getTextStyle = (content) => {
                                                        if (content === 'L' && isLeaveDay) {
                                                            return styles.leaveText;
                                                        } else if (content === 'H' && isWeekend) {
                                                            return styles.weekendText;
                                                        } else if (isPartialDay && !isLeaveDay && !isWeekend) {
                                                            return styles.partialDayText;
                                                        }
                                                        return null;
                                                    };

                                                    // Check if this is the last row to remove bottom border
                                                    if (index === currentRecords.length - 1) {
                                                        rowStyle.push({ borderBottomWidth: 0 });
                                                    }

                                                    return (
                                                        <View
                                                            key={record.date}
                                                            style={rowStyle}
                                                        >
                                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.dayOfWeek}</Text>
                                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.formattedDate}</Text>
                                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkInDisplay)]}>
                                                                {checkInDisplay}
                                                            </Text>
                                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkOutDisplay)]}>
                                                                {checkOutDisplay}
                                                            </Text>
                                                            <Text style={[styles.tableCell, { flex: 1.5 }, getTextStyle(hoursDisplay)]}>
                                                                {hoursDisplay}
                                                            </Text>
                                                        </View>
                                                    );
                                                })}

                                                {/* Pagination Controls */}
                                                <View style={styles.paginationContainer}>
                                                    <TouchableOpacity
                                                        style={[styles.paginationButton, currentPage === 1 && styles.disabledButton]}
                                                        onPress={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                                        disabled={currentPage === 1}
                                                    >
                                                        <Text style={styles.paginationButtonText}>Previous</Text>
                                                    </TouchableOpacity>

                                                    <Text style={styles.paginationText}>
                                                        Page {currentPage} of {totalPages}
                                                    </Text>

                                                    <TouchableOpacity
                                                        style={[styles.paginationButton, currentPage === totalPages && styles.disabledButton]}
                                                        onPress={() => setCurrentPage(prev =>
                                                            Math.min(totalPages, prev + 1)
                                                        )}
                                                        disabled={currentPage === totalPages}
                                                    >
                                                        <Text style={styles.paginationButtonText}>Next</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            </>
                                        );
                                    })()
                                )}
                            </View>
                        )}

                        {/* Export Button */}
                        <TouchableOpacity
                            style={styles.exportButton}
                            onPress={exportAttendanceRecords}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="file-export" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Export Records</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    scrollContainer: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    container: {
        flex: 1,
        marginTop: 25,
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#f0f0f0',
    },
    header: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        borderRadius: 15,
        backgroundColor: '#f8f8f8',
        marginBottom: 15,
    },
    title: {
        fontSize: 19,
        fontWeight: 'bold',
    },

    // Modern attendance card
    attendanceCard: {
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.27,
        shadowRadius: 4.65,
        elevation: 6,
        width: '100%',
        marginBottom: 10,
    },
    timeStatusItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
        paddingBottom: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    timeStatusContent: {
        marginLeft: 15,
        flex: 1,
    },
    timeStatusLabel: {
        fontSize: 14,
        color: '#666',
        marginBottom: 0,
    },
    timeStatusValue: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
    },

    // Button styles
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
        marginBottom: 20,
    },
    checkinButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        width: 155,
        shadowColor: '#007AFF',
    },
    checkoutButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        width: 155,
    },
    disabledButton: {
        backgroundColor: '#A9A9A9',
        shadowOpacity: 0.1,
    },
    buttonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
        marginLeft: 8,
    },
    buttonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },

    // Weekly Attendance Records Section
    sectionContainer: {
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.27,
        shadowRadius: 4.65,
        elevation: 6,
        width: '100%',
        marginTop: -5,
        marginBottom: 20,
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 15,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginLeft: 10,
    },
    weekNavigation: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 15,
    },
    weekNavButton: {
        padding: 5,
        borderRadius: 5,
        backgroundColor: '#f0f0f0',
    },
    currentWeekButton: {
        padding: 8,
        borderRadius: 8,
        backgroundColor: '#f0f0f0',
        flex: 1,
        marginHorizontal: 10,
        alignItems: 'center',
    },
    currentWeekText: {
        fontSize: 14,
        fontWeight: '600',
        color: '#007AFF',
    },

    // Table styles
    tableContainer: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 15,
        marginLeft: -5,
        marginRight: -5,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#007AFF',
        padding: 10,
    },
    tableHeaderCell: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 13,
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 5,
    },
    evenRow: {
        backgroundColor: '#f9f9f9',
    },
    oddRow: {
        backgroundColor: 'white',
    },
    leaveDayRow: {
        backgroundColor: '#FFEBEE',
    },
    leaveText: {
        color: '#F44336',
        fontWeight: 'bold',
    },
    weekendWorkRow: {
        backgroundColor: '#E3F2FD',
    },
    weekendText: {
        color: '#007AFF',
        fontWeight: 'bold',
    },
    partialDayRow: {
        backgroundColor: '#FFF3E0',
    },
    partialDayText: {
        color: '#FF9800',
        fontWeight: 'bold',
    },
    tableCell: {
        fontSize: 13,
        textAlign: 'center',
    },

    // Export button
    exportButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        alignSelf: 'center',
        width: '60%',
    },

    // Loading indicator
    loader: {
        marginVertical: 20,
    },

    // View type button
    viewTypeButton: {
        flexDirection: 'row',
        alignItems: 'justify-center',
        marginLeft: 10,
        paddingVertical: 5,
        paddingHorizontal: 5,
        borderRadius: 5,
        backgroundColor: '#f0f0f0',
    },

    // Modal styles
    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
        width: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalOption: {
        paddingVertical: 15,
        paddingHorizontal: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    selectedOption: {
        backgroundColor: '#E3F2FD',
    },
    modalOptionText: {
        fontSize: 16,
        color: '#333',
    },
    selectedOptionText: {
        color: '#007AFF',
        fontWeight: 'bold',
    },

    // Pagination styles
    paginationContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        // marginTop: 10,
        paddingTop: 5,
        borderTopWidth: 1,
        borderTopColor: '#ddd',
    },
    paginationButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 5,
    },
    paginationButtonText: {
        color: 'white',
        fontWeight: 'bold',
        marginLeft: 5,
    },
    paginationText: {
        fontSize: 14,
        color: '#666',
    },
});



//---------------------------------------------------------------------------------------


PaidHolidays.js

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ScrollView, ActivityIndicator } from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { collection, addDoc, getDocs, deleteDoc, doc, query, where } from 'firebase/firestore';
import { Icon } from 'react-native-elements';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { format, parse, isValid } from 'date-fns';
import { Picker } from '@react-native-picker/picker';

export default function PaidHolidays({ navigation }) {
    const [holidays, setHolidays] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
    const [years] = useState(() => {
        const currentYear = new Date().getFullYear();
        return Array.from({ length: 5 }, (_, i) => currentYear + i);
    });

    useEffect(() => {
        fetchHolidays();
    }, [selectedYear]);

    const fetchHolidays = async () => {
        try {
            setIsLoading(true);
            const holidaysQuery = query(
                collection(FIRESTORE_DB, 'paidHolidays'),
                where('year', '==', selectedYear)
            );
            const querySnapshot = await getDocs(holidaysQuery);
            const holidaysList = querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
            setHolidays(holidaysList);
        } catch (error) {
            console.error('Error fetching holidays:', error);
            Alert.alert('Error', 'Failed to fetch holidays');
        } finally {
            setIsLoading(false);
        }
    };

    const handleFileUpload = async () => {
        try {
            const result = await DocumentPicker.getDocumentAsync({
                type: ['text/csv', 'application/vnd.ms-excel'],
                copyToCacheDirectory: true
            });

            if (result.type === 'success') {
                setIsLoading(true);
                const fileContent = await FileSystem.readAsStringAsync(result.uri);
                const lines = fileContent.split('\n');
                
                // Skip header row
                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line) continue;

                    const [date, description] = line.split(',').map(item => item.trim());
                    if (!date || !description) continue;

                    // Parsing date (DD/MM/YYYY)
                    const parsedDate = parse(date, 'dd/MM/yyyy', new Date());
                    if (!isValid(parsedDate)) {
                        console.error(`Invalid date format: ${date}`);
                        continue;
                    }

                    const formattedDate = format(parsedDate, 'dd-MM-yyyy');
                    const year = parsedDate.getFullYear();

                    // Add to Firestore
                    await addDoc(collection(FIRESTORE_DB, 'paidHolidays'), {
                        date: formattedDate,
                        description,
                        year,
                        createdAt: new Date().toISOString(),
                        createdBy: FIREBASE_AUTH.currentUser?.uid
                    });
                }

                Alert.alert('Success', 'Holidays uploaded successfully');
                fetchHolidays();
            }
        } catch (error) {
            console.error('Error uploading file:', error);
            Alert.alert('Error', 'Failed to upload holidays file');
        } finally {
            setIsLoading(false);
        }
    };

    const deleteHoliday = async (holidayId) => {
        try {
            await deleteDoc(doc(FIRESTORE_DB, 'paidHolidays', holidayId));
            setHolidays(holidays.filter(h => h.id !== holidayId));
            Alert.alert('Success', 'Holiday deleted successfully');
        } catch (error) {
            console.error('Error deleting holiday:', error);
            Alert.alert('Error', 'Failed to delete holiday');
        }
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.openDrawer()}>
                    <Icon name="menu" size={25} color="#333" />
                </TouchableOpacity>
                <Text style={styles.title}>Manage Paid Holidays</Text>
                <TouchableOpacity onPress={handleFileUpload}>
                    <Icon name="upload" size={25} color="#333" />
                </TouchableOpacity>
            </View>

            <View style={styles.yearSelector}>
                <Text style={styles.yearLabel}>Select Year:</Text>
                <Picker
                    selectedValue={selectedYear}
                    style={styles.yearPicker}
                    onValueChange={(itemValue) => setSelectedYear(itemValue)}
                >
                    {years.map(year => (
                        <Picker.Item key={year} label={year.toString()} value={year} />
                    ))}
                </Picker>
            </View>

            {isLoading ? (
                <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
            ) : (
                <ScrollView style={styles.holidaysList}>
                    {holidays.length === 0 ? (
                        <Text style={styles.noHolidays}>No holidays found for {selectedYear}</Text>
                    ) : (
                        holidays.map(holiday => (
                            <View key={holiday.id} style={styles.holidayItem}>
                                <View style={styles.holidayInfo}>
                                    <Text style={styles.holidayDate}>
                                        {format(new Date(holiday.date), 'dd-MM-yyyy')}
                                    </Text>
                                    <Text style={styles.holidayDescription}>{holiday.description}</Text>
                                </View>
                                <TouchableOpacity
                                    onPress={() => deleteHoliday(holiday.id)}
                                    style={styles.deleteButton}
                                >
                                    <Icon name="delete" size={24} color="#FF3B30" />
                                </TouchableOpacity>
                            </View>
                        ))
                    )}
                </ScrollView>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 16,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        marginTop: 25,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    yearSelector: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#fff',
        marginTop: 8,
    },
    yearLabel: {
        fontSize: 16,
        marginRight: 10,
    },
    yearPicker: {
        flex: 1,
        height: 50,
    },
    holidaysList: {
        flex: 1,
        padding: 16,
    },
    holidayItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
        padding: 16,
        borderRadius: 8,
        marginBottom: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },
    holidayInfo: {
        flex: 1,
    },
    holidayDate: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    holidayDescription: {
        fontSize: 14,
        color: '#666',
    },
    deleteButton: {
        padding: 8,
    },
    loader: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    noHolidays: {
        textAlign: 'center',
        fontSize: 16,
        color: '#666',
        marginTop: 20,
    },
});



//-----------------------------------------------------------------------------

"expo-document-picker": "^13.1.5",




//---------------------------------------------------------------------------------------------------------------------------------

Previous codes

AdminAttendance.js


import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ScrollView, ActivityIndicator, FlatList } from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { collection, query, getDocs, where, orderBy } from 'firebase/firestore';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addWeeks, subWeeks } from 'date-fns';
import { Icon } from 'react-native-elements';
import { Picker } from '@react-native-picker/picker';

export default function AdminAttendance({ navigation }) {
    const [isAdmin, setIsAdmin] = useState(false);
    const [username, setUsername] = useState('');
    const [users, setUsers] = useState([]);
    const [selectedUser, setSelectedUser] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    
    // Week selection for attendance records
    const currentDate = new Date();
    const [selectedWeek, setSelectedWeek] = useState(currentDate);
    const [weeklyAttendance, setWeeklyAttendance] = useState([]);

    useEffect(() => {
        checkAdminAccess();
        fetchUsers();
    }, []);

    useEffect(() => {
        if (selectedUser) {
            fetchWeeklyAttendance();
        }
    }, [selectedUser, selectedWeek]);

    // Check if current user is admin
    const checkAdminAccess = async () => {
        try {
            const currentUser = FIREBASE_AUTH.currentUser;
            if (!currentUser) {
                navigation.replace('Login');
                return;
            }

            const userDoc = await getDocs(
                query(
                    collection(FIRESTORE_DB, 'users'),
                    where('email', '==', currentUser.email)
                )
            );

            if (!userDoc.empty) {
                const userData = userDoc.docs[0].data();
                setUsername(userData.username);
                setIsAdmin(userData.role === 'admin');
                if (userData.role !== 'admin') {
                    Alert.alert('Access Denied', 'Only admins can access this screen');
                    navigation.replace('Dashboard');
                }
            }
        } catch (error) {
            console.error('Error checking admin status:', error);
            Alert.alert('Error', 'Failed to verify admin access');
        }
    };

    // Fetch all users
    const fetchUsers = async () => {
        try {
            const querySnapshot = await getDocs(collection(FIRESTORE_DB, 'users'));
            const usersList = querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
            setUsers(usersList);
        } catch (error) {
            console.error('Error fetching users:', error);
            Alert.alert('Error', 'Failed to load users');
        }
    };

    // Function to fetch attendance for a specific date
    const fetchAttendanceForDate = async (formattedDate) => {
        if (!selectedUser) return null;

        try {
            // Format the date to match the document ID format
            const docId = `${selectedUser.id}_${formattedDate.replace(/\//g, '-')}`;

            // Query the attendance collection
            const q = query(
                collection(FIRESTORE_DB, 'attendance'),
                where('docId', '==', docId)
            );
            
            const querySnapshot = await getDocs(q);
            
            if (!querySnapshot.empty) {
                return querySnapshot.docs[0].data();
            } else {
                return null;
            }
        } catch (error) {
            console.error('Error fetching attendance record:', error);
            return null;
        }
    };

    // Function to fetch weekly attendance records
    const fetchWeeklyAttendance = async () => {
        if (!selectedUser) return;

        setIsLoading(true);
        try {
            // Ensure selectedWeek is a valid Date object
            const weekDate = new Date(selectedWeek);

            // Create date range for the selected week (Sunday to Saturday)
            const start = startOfWeek(weekDate, { weekStartsOn: 0 });
            const end = endOfWeek(weekDate, { weekStartsOn: 0 });

            // Get all days in the week
            const daysInWeek = eachDayOfInterval({ start, end });

            // Initialize attendance data with empty values for all days
            const initialAttendance = daysInWeek.map(day => ({
                date: format(day, 'yyyy-MM-dd'),
                formattedDate: format(day, 'dd-MM'), // day and month
                dayOfWeek: format(day, 'EEE'),
                checkInTime: null,
                checkOutTime: null,
                workedHours: '-',
                totalWorkedMinutes: 0
            }));

            // Process each day in the week
            const attendanceData = {};
            for (const day of daysInWeek) {
                try {
                    // Format the date to match what's stored in Firestore
                    const dateFormatted = day.toLocaleDateString();

                    // Fetch attendance for this specific day
                    const attendanceRecord = await fetchAttendanceForDate(dateFormatted);

                    if (attendanceRecord) {
                        // If record exists, add it to our data
                        const dateKey = format(day, 'yyyy-MM-dd');
                        attendanceData[dateKey] = {
                            checkInTime: attendanceRecord.checkInTime,
                            checkOutTime: attendanceRecord.checkOutTime,
                            workedHours: attendanceRecord.workedHours || '-',
                            totalWorkedMinutes: attendanceRecord.totalWorkedMinutes || 0
                        };
                    }
                } catch (error) {
                    console.error(`Error fetching attendance for ${format(day, 'yyyy-MM-dd')}:`, error);
                }
            }

            // Update the initialAttendance array with actual data
            const updatedAttendance = initialAttendance.map(day => {
                const dateKey = day.date;
                if (attendanceData[dateKey]) {
                    return {
                        ...day,
                        checkInTime: attendanceData[dateKey].checkInTime,
                        checkOutTime: attendanceData[dateKey].checkOutTime,
                        workedHours: attendanceData[dateKey].workedHours,
                        totalWorkedMinutes: attendanceData[dateKey].totalWorkedMinutes
                    };
                }
                return day;
            });

            setWeeklyAttendance(updatedAttendance);
        } catch (error) {
            console.error('Error fetching weekly attendance:', error);
            Alert.alert('Error', 'Failed to load attendance records');
        } finally {
            setIsLoading(false);
        }
    };

    // Function to navigate to previous week
    const goToPreviousWeek = () => {
        setSelectedWeek(prevWeek => subWeeks(prevWeek, 1));
    };

    // Function to navigate to next week
    const goToNextWeek = () => {
        setSelectedWeek(prevWeek => addWeeks(prevWeek, 1));
    };

    // Format date-time for display
    const formatDateTime = (dateTimeString) => {
        if (!dateTimeString) return '-';
        const date = new Date(dateTimeString);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    // Handle user selection
    const handleUserChange = (userId) => {
        const user = users.find(u => u.id === userId);
        setSelectedUser(user);
    };

    // Handle logout
    const handleLogout = () => {
        FIREBASE_AUTH.signOut()
            .then(() => {
                console.log('User logged out');
                navigation.navigate('Login');
            })
            .catch((error) => {
                console.error('Error logging out:', error);
            });
    };

    if (!isAdmin) {
        return null;
    }

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.openDrawer()}>
                    <Icon name="menu" size={23} color="#333" />
                </TouchableOpacity>
                <Text style={styles.title}>Attendance Management</Text>
                <TouchableOpacity onPress={handleLogout}>
                    <Icon name="logout" size={23} color="#333" />
                </TouchableOpacity>
            </View>

            <View style={styles.userSelectionContainer}>
                <Text style={styles.sectionTitle}>Select Employee:</Text>
                <View style={styles.pickerContainer}>
                    <Picker
                        selectedValue={selectedUser?.id}
                        onValueChange={handleUserChange}
                        style={styles.picker}
                    >
                        <Picker.Item label="Select an employee" value="" />
                        {users.map(user => (
                            <Picker.Item key={user.id} label={user.username} value={user.id} />
                        ))}
                    </Picker>
                </View>
            </View>

            {selectedUser && (
                <View style={styles.attendanceContainer}>
                    <Text style={styles.sectionTitle}>
                        Attendance Records for {selectedUser.username}
                    </Text>

                    <View style={styles.weekSelector}>
                        <TouchableOpacity onPress={goToPreviousWeek} style={styles.weekButton}>
                            <Icon name="chevron-left" size={24} color="#007AFF" />
                        </TouchableOpacity>
                        <Text style={styles.weekText}>
                            {format(startOfWeek(selectedWeek, { weekStartsOn: 0 }), 'dd MMM')} - {format(endOfWeek(selectedWeek, { weekStartsOn: 0 }), 'dd MMM yyyy')}
                        </Text>
                        <TouchableOpacity onPress={goToNextWeek} style={styles.weekButton}>
                            <Icon name="chevron-right" size={24} color="#007AFF" />
                        </TouchableOpacity>
                    </View>

                    {isLoading ? (
                        <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
                    ) : (
                        <ScrollView horizontal={true} style={styles.tableContainer}>
                            <View>
                                <View style={styles.tableHeader}>
                                    <Text style={[styles.headerCell, { width: 80 }]}>Date</Text>
                                    <Text style={[styles.headerCell, { width: 80 }]}>Day</Text>
                                    <Text style={[styles.headerCell, { width: 100 }]}>Check In</Text>
                                    <Text style={[styles.headerCell, { width: 100 }]}>Check Out</Text>
                                    <Text style={[styles.headerCell, { width: 120 }]}>Worked Hours</Text>
                                </View>
                                <FlatList
                                    data={weeklyAttendance}
                                    keyExtractor={(item) => item.date}
                                    renderItem={({ item }) => {
                                        // Determine if it's a weekend
                                        const isWeekend = item.dayOfWeek === 'Sat' || item.dayOfWeek === 'Sun';
                                        
                                        // For weekends with no attendance, show 'H'
                                        const displayedHours = isWeekend && !item.checkInTime ? 'H' : item.workedHours;
                                        
                                        return (
                                            <View style={styles.tableRow}>
                                                <Text style={[styles.cell, { width: 80 }]}>{item.formattedDate}</Text>
                                                <Text style={[styles.cell, { width: 80 }]}>{item.dayOfWeek}</Text>
                                                <Text style={[styles.cell, { width: 100 }]}>
                                                    {item.checkInTime ? formatDateTime(item.checkInTime) : '-'}
                                                </Text>
                                                <Text style={[styles.cell, { width: 100 }]}>
                                                    {item.checkOutTime ? formatDateTime(item.checkOutTime) : '-'}
                                                </Text>
                                                <Text style={[styles.cell, { width: 120 }]}>
                                                    {displayedHours}
                                                </Text>
                                            </View>
                                        );
                                    }}
                                />
                            </View>
                        </ScrollView>
                    )}
                </View>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginTop: 25,
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#f0f0f0',
    },
    header: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        borderRadius: 15,
        backgroundColor: '#f8f8f8',
        marginBottom: 15,
    },
    title: {
        fontSize: 19,
        fontWeight: 'bold',
    },
    userSelectionContainer: {
        width: '90%',
        marginBottom: 15,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 10,
        alignSelf: 'center',
    },
    pickerContainer: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        marginBottom: 10,
    },
    picker: {
        height: 50,
    },
    attendanceContainer: {
        flex: 1,
    },
    weekSelector: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 15,
    },
    weekButton: {
        padding: 5,
    },
    weekText: {
        fontSize: 16,
        fontWeight: '500',
    },
    tableContainer: {
        flex: 1,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#f0f0f0',
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
    },
    headerCell: {
        fontWeight: 'bold',
        paddingHorizontal: 10,
    },
    tableRow: {
        flexDirection: 'row',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    cell: {
        paddingHorizontal: 10,
    },
    loader: {
        marginTop: 50,
    },
});




//---------------------------------------------------------------------------------------------------------------------


Attendance.js

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ScrollView, ActivityIndicator, Modal, Share, Platform } from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { getDoc, doc, setDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addWeeks, subWeeks } from 'date-fns';
import XLSX from 'xlsx';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Location from 'expo-location';
import { Icon } from 'react-native-elements';

export default function Attendance({ navigation }) {
    const [username, setUsername] = useState('');
    const [checkInTime, setCheckInTime] = useState(null);
    const [checkOutTime, setCheckOutTime] = useState(null);
    const [workedHours, setWorkedHours] = useState(null);
    const [totalWorkedMinutes, setTotalWorkedMinutes] = useState(0);
    const [isCheckInDisabled, setIsCheckInDisabled] = useState(false);
    const [isCheckOutDisabled, setIsCheckOutDisabled] = useState(true);

    // Location states
    const [checkInLocation, setCheckInLocation] = useState(null);
    const [checkOutLocation, setCheckOutLocation] = useState(null);
    const [locationPermission, setLocationPermission] = useState(false);

    // Week and month selection for attendance records
    const currentDate = new Date();
    const [selectedWeek, setSelectedWeek] = useState(currentDate);
    const [selectedMonth, setSelectedMonth] = useState(currentDate);
    const [weeklyAttendance, setWeeklyAttendance] = useState([]);
    const [monthlyAttendance, setMonthlyAttendance] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [leaveDays, setLeaveDays] = useState({});

    // View type and modal state
    const [viewType, setViewType] = useState('weekly');
    const [modalVisible, setModalVisible] = useState(false);

    // Pagination for monthly view
    const [recordsPerPage] = useState(7);
    const [currentPage, setCurrentPage] = useState(1);

    const user = FIREBASE_AUTH.currentUser;

    useEffect(() => {
        const fetchUserData = async () => {
            const user = FIREBASE_AUTH.currentUser;
            if (user) {
                const userDoc = await getDoc(doc(FIRESTORE_DB, 'users', user.uid));
                if (userDoc.exists()) {
                    setUsername(userDoc.data().username);
                } else {
                    setUsername('User');
                }
            } else {
                setUsername('Guest');
            }
        };

        fetchUserData();
    }, []);

    useEffect(() => {
        fetchAttendance();
        requestLocationPermission();
    }, []);

    // Request location permission with enhanced error handling for SDK 53
    const requestLocationPermission = async () => {
        try {
            // First check if location services are enabled
            const isLocationServicesEnabled = await Location.hasServicesEnabledAsync();

            if (!isLocationServicesEnabled) {
                console.log('Location services are disabled');
                Alert.alert(
                    "Location Services Disabled",
                    "Please enable location services in your device settings to use location features.",
                    [{ text: "OK" }]
                );
                return;
            }

            // Check foreground permissions
            const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();

            if (foregroundStatus === 'granted') {
                console.log('Location permission granted');
                setLocationPermission(true);
            } else {
                console.log('Location permission denied');
                Alert.alert(
                    "Permission Required",
                    "Location permission is required to track check-in and check-out locations.",
                    [{ text: "OK" }]
                );
            }
        } catch (error) {
            console.error('Error requesting location permission:', error);
            Alert.alert(
                "Permission Error",
                "There was an error requesting location permissions. Please try again or check app permissions in settings.",
                [{ text: "OK" }]
            );
        }
    };

    // Get current location with enhanced error handling and timeout for SDK 53
    const getCurrentLocation = async () => {
        if (!locationPermission) {
            console.log('Location permission not granted, requesting permission');
            await requestLocationPermission();

            // If still not granted after request, return null
            if (!locationPermission) {
                return null;
            }
        }

        try {
            // Set a timeout for location request
            const locationPromise = Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.High,
                timeInterval: 5000, // Update at most every 5 seconds
                distanceInterval: 10, // Update if moved by 10 meters
            });

            // Create a timeout promise
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Location request timed out')), 15000)
            );

            // Race the location request against the timeout
            const location = await Promise.race([locationPromise, timeoutPromise]);

            if (!location) {
                throw new Error('Failed to get location');
            }

            // Get address from coordinates with error handling
            let addressResponse;
            try {
                addressResponse = await Location.reverseGeocodeAsync({
                    latitude: location.coords.latitude,
                    longitude: location.coords.longitude
                });
            } catch (geocodeError) {
                console.error('Error getting address from coordinates:', geocodeError);
                // Continue with unknown location if geocoding fails
            }

            // Format the address
            let formattedAddress = 'Unknown location';
            if (addressResponse && addressResponse.length > 0) {
                const address = addressResponse[0];
                const addressParts = [];

                if (address.name) addressParts.push(address.name);
                if (address.street) addressParts.push(address.street);
                if (address.city) addressParts.push(address.city);
                if (address.region) addressParts.push(address.region);

                formattedAddress = addressParts.join(', ');
            }

            return {
                coords: {
                    latitude: location.coords.latitude,
                    longitude: location.coords.longitude
                },
                address: formattedAddress,
                timestamp: new Date()
            };
        } catch (error) {
            console.error('Error getting current location:', error);

            // Show a user-friendly error message
            if (error.message === 'Location request timed out') {
                Alert.alert(
                    "Location Timeout",
                    "Unable to get your current location. Please check if location services are enabled and try again.",
                    [{ text: "OK" }]
                );
            } else {
                Alert.alert(
                    "Location Error",
                    "There was a problem getting your location. Please try again later.",
                    [{ text: "OK" }]
                );
            }

            return null;
        }
    };

    // Fetch attendance records when selected week/month changes
    useEffect(() => {
        console.log('Selected week changed:', selectedWeek);
        fetchLeaveData();
        fetchWeeklyAttendance();
    }, [selectedWeek]);

    // Fetch monthly attendance when selected month changes
    useEffect(() => {
        if (viewType === 'monthly') {
            console.log('Selected month changed:', selectedMonth);
            fetchMonthlyAttendance();
        }
    }, [selectedMonth, viewType]);

    // Ensure leave days are properly maintained when changing pages in monthly view
    useEffect(() => {
        if (viewType === 'monthly') {
            // Get the year and month from selectedMonth
            const year = selectedMonth.getFullYear();
            const month = selectedMonth.getMonth();

            // Create the first and last day of the month
            const firstDayOfMonth = new Date(year, month, 1);
            const lastDayOfMonth = new Date(year, month + 1, 0);

            // Refresh leave data when changing pages
            fetchMonthLeaveData(firstDayOfMonth, lastDayOfMonth);
        }
    }, [currentPage, viewType]);

    // Function to fetch approved leave requests
    const fetchLeaveData = async () => {
        if (!user) return;

        try {
            // Create date range for the selected week
            const weekDate = new Date(selectedWeek);
            const startOfWeekDate = startOfWeek(weekDate, { weekStartsOn: 0 });
            const endOfWeekDate = endOfWeek(weekDate, { weekStartsOn: 0 });

            console.log('Fetching leave data for week:', format(startOfWeekDate, 'yyyy-MM-dd'), 'to', format(endOfWeekDate, 'yyyy-MM-dd'));

            // Query approved leave requests for the current user
            const leaveQuery = query(
                collection(FIRESTORE_DB, 'leaveRequests'),
                where('userId', '==', user.uid),
                where('status', '==', 'Approved')
            );

            const querySnapshot = await getDocs(leaveQuery);
            console.log('Found', querySnapshot.size, 'approved leave requests');

            const leaveData = {};

            // Process each leave request
            querySnapshot.forEach(doc => {
                const leave = doc.data();
                console.log('Processing leave request:', leave);

                // Convert string dates to Date objects
                const startDate = new Date(leave.startDate);
                const endDate = new Date(leave.endDate);

                console.log('Leave period:', format(startDate, 'yyyy-MM-dd'), 'to', format(endDate, 'yyyy-MM-dd'));

                // Mark all days in the leave period
                const currentDate = new Date(startDate);
                while (currentDate <= endDate) {
                    const dateStr = format(currentDate, 'yyyy-MM-dd');

                    // Check if the leave day is within the selected week
                    if (currentDate >= startOfWeekDate && currentDate <= endOfWeekDate) {
                        leaveData[dateStr] = true;
                        console.log('Marked leave day:', dateStr);
                    }

                    // Move to next day
                    currentDate.setDate(currentDate.getDate() + 1);
                }
            });

            console.log('Final leave days data:', leaveData);
            setLeaveDays(leaveData);
        } catch (error) {
            console.error('Error fetching leave data:', error);
        }
    };

    const fetchAttendance = async () => {
        if (!user) return;

        try {
            const todayDate = new Date().toLocaleDateString();

            // document ID includes user ID and date. A unique document for each user for each day created
            const docId = `${user.uid}_${todayDate.replace(/\//g, '-')}`;

            // Reference to today's attendance document
            const docRef = doc(FIRESTORE_DB, 'attendance', docId);
            const docSnap = await getDoc(docRef);

            if (docSnap.exists()) {
                // Document exists for today, use its data
                const data = docSnap.data();
                setCheckInTime(data.checkInTime);
                setCheckOutTime(data.checkOutTime);
                setWorkedHours(data.workedHours);
                setTotalWorkedMinutes(data.totalWorkedMinutes || 0);

                // Set location data if available
                if (data.checkInLocation) {
                    setCheckInLocation(data.checkInLocation);
                }
                if (data.checkOutLocation) {
                    setCheckOutLocation(data.checkOutLocation);
                }

                // Disable/Enable Buttons
                if (data.checkInTime) {
                    setIsCheckInDisabled(true);
                    // Always enable check-out if checked in
                    setIsCheckOutDisabled(false);
                } else {
                    setIsCheckInDisabled(false);
                    setIsCheckOutDisabled(true);
                }
            } else {
                // No document exists for today, reset UI
                console.log("No attendance record found for today.");
                setCheckInTime(null);
                setCheckOutTime(null);
                setWorkedHours('');
                setTotalWorkedMinutes(0);
                setCheckInLocation(null);
                setCheckOutLocation(null);
                setIsCheckInDisabled(false);
                setIsCheckOutDisabled(true);
            }
        } catch (error) {
            console.error("Error fetching attendance:", error);
        }
    };

    // Check-In
    const handleCheckIn = async () => {
        if (!user) return;

        try {
            const checkInTimestamp = new Date().toISOString();
            const todayDate = new Date().toLocaleDateString();

            // Create a document ID that includes the user ID and date
            const docId = `${user.uid}_${todayDate.replace(/\//g, '-')}`;

            // Get current location
            const locationData = await getCurrentLocation();

            setCheckInTime(checkInTimestamp);
            setCheckOutTime(null);
            if (locationData) {
                setCheckInLocation(locationData);
            }
            setIsCheckInDisabled(true);
            setIsCheckOutDisabled(false);

            // Reference to today's attendance document
            const docRef = doc(FIRESTORE_DB, 'attendance', docId);

            // Create a new document for today with check-in data
            await setDoc(docRef, {
                checkInTime: checkInTimestamp,
                checkOutTime: null,
                workedHours: '0 hrs 0 mins',
                totalWorkedMinutes: 0,
                userId: user.uid,
                email: user.email,
                username: username,
                date: todayDate,
                timestamp: new Date(),
                docId: docId,
                checkInLocation: locationData || null,
                checkOutLocation: null
            });

            console.log("Check-In saved in Firestore:", checkInTimestamp);
            let successMessage = `Checked In at ${formatDateTime(checkInTimestamp)}!`;
            if (locationData) {
                successMessage += `\nLocation: ${locationData.address}`;
            }
            Alert.alert("Success", successMessage);
        } catch (error) {
            console.error("Error during check-in:", error);
            Alert.alert("Error", "Failed to Check In");
        }
    };

    // Check-Out
    const handleCheckOut = async () => {
        if (!user || !checkInTime) return;

        try {
            const checkOutTimestamp = new Date().toISOString();
            const todayDate = new Date().toLocaleDateString();

            // document ID that includes the user ID and date
            const docId = `${user.uid}_${todayDate.replace(/\//g, '-')}`;

            // Get current location
            const locationData = await getCurrentLocation();

            // Calculate time worked for this session
            const diffMs = new Date(checkOutTimestamp) - new Date(checkInTime);
            const session = Math.floor(diffMs / (1000 * 60));

            // Calculate total hours and minutes for display
            const totalHours = Math.floor(session / 60);
            const remainingMinutes = session % 60;
            const totalWorkedDuration = `${totalHours} hrs ${remainingMinutes} mins`;

            // Session duration calculation removed as it's not needed

            // Reference to today's attendance document
            const attendanceRef = doc(FIRESTORE_DB, 'attendance', docId);

            if (locationData) {
                setCheckOutLocation(locationData);
            }

            // Update today's document with check-out data
            await updateDoc(attendanceRef, {
                checkOutTime: checkOutTimestamp,
                workedHours: totalWorkedDuration,
                totalWorkedMinutes: session,
                checkOutLocation: locationData || null
            });

            setCheckOutTime(checkOutTimestamp);
            setWorkedHours(totalWorkedDuration);
            setTotalWorkedMinutes(session);

            let successMessage = `Checked Out - ${formatDateTime(checkOutTimestamp)}!\nWorked Hours: ${totalWorkedDuration}`;
            if (locationData) {
                successMessage += `\nLocation: ${locationData.address}`;
            }

            Alert.alert("Success", successMessage);

        } catch (error) {
            console.error("Error during check-out:", error);
            Alert.alert("Error", "Failed to Check Out");
        }
    };

    // function to format date strings
    const formatDateTime = (dateTimeString) => {
        if (!dateTimeString) return 'Not Available';

        try {
            const date = new Date(dateTimeString);
            return format(date, 'dd-MM-yyyy HH:mm');
        } catch (error) {
            console.error('Error formatting date:', error);
            return dateTimeString;
        }
    };

    // function to format only time part of date strings
    const formatTimeOnly = (dateTimeString) => {
        if (!dateTimeString) return null;

        try {
            const date = new Date(dateTimeString);
            return format(date, 'HH:mm');
        } catch (error) {
            console.error('Error formatting time:', error);
            return null;
        }
    };

    /**
     * Fetches attendance record for a specific date
     * @param {string} date - The date in format MM/DD/YYYY or a Date object
     * @returns {Promise<Object|null>} - The attendance record or null if not found
     */
    const fetchAttendanceForDate = async (date) => {
        if (!user) return null;

        try {
            let formattedDate;

            // Handle different date formats
            if (date instanceof Date) {
                // If Date object, convert to local date string
                formattedDate = date.toLocaleDateString();
            }
            else if (typeof date === 'string') {
                // string, use directly
                formattedDate = date;
            }
            else {
                console.error('Invalid date format provided');
                return null;
            }

            // Format the date to match the document ID format
            const docId = `${user.uid}_${formattedDate.replace(/\//g, '-')}`;

            console.log(`Fetching attendance for date: ${formattedDate}, docId: ${docId}`);

            // Reference to the specific attendance document
            const docRef = doc(FIRESTORE_DB, 'attendance', docId);

            // Get the document
            const docSnap = await getDoc(docRef);

            if (docSnap.exists()) {
                // Return the attendance data
                console.log(`Found attendance record for ${formattedDate}`);
                return docSnap.data();
            } else {
                console.log(`No attendance record found for ${formattedDate}`);
                return null;
            }
        } catch (error) {
            console.error('Error fetching attendance record:', error);
            return null;
        }
    };

    // Function to fetch weekly attendance records
    const fetchWeeklyAttendance = async () => {
        if (!user) return;

        setIsLoading(true);
        try {
            // Ensure selectedWeek is a valid Date object
            const weekDate = new Date(selectedWeek);

            // Create date range for the selected week (Sunday to Saturday)
            console.log('Fetching weekly attendance, selectedWeek:', selectedWeek);
            console.log('Converted weekDate:', weekDate);

            // Use consistent options for both start and end of week
            const start = startOfWeek(weekDate, { weekStartsOn: 0 });
            const end = endOfWeek(weekDate, { weekStartsOn: 0 });

            console.log('Week range:', start, 'to', end);

            // Get all days in the week
            const daysInWeek = eachDayOfInterval({ start, end });

            // Initialize attendance data with empty values for all days
            const initialAttendance = daysInWeek.map(day => ({
                date: format(day, 'yyyy-MM-dd'),
                formattedDate: format(day, 'dd-MM'), // day and month
                dayOfWeek: format(day, 'EEE'),
                checkInTime: null,
                checkOutTime: null,
                workedHours: 'N/A',
                totalWorkedMinutes: 0
            }));

            // More efficient approach: Fetch attendance records for each day in the week
            const attendanceData = {};

            // Process each day in the week
            for (const day of daysInWeek) {
                try {
                    // Format the date to match what's stored in Firestore
                    const dateFormatted = day.toLocaleDateString();

                    // Fetch attendance for this specific day
                    const attendanceRecord = await fetchAttendanceForDate(dateFormatted);

                    if (attendanceRecord) {
                        // If record exists, add it to our data
                        const dateKey = format(day, 'yyyy-MM-dd');
                        attendanceData[dateKey] = {
                            checkInTime: attendanceRecord.checkInTime,
                            checkOutTime: attendanceRecord.checkOutTime,
                            workedHours: attendanceRecord.workedHours || 'N/A',
                            totalWorkedMinutes: attendanceRecord.totalWorkedMinutes || 0
                        };
                    }
                } catch (error) {
                    console.error(`Error fetching attendance for ${format(day, 'yyyy-MM-dd')}:`, error);
                }
            }

            // Update the initialAttendance array with actual data
            const updatedAttendance = initialAttendance.map(day => {
                const dateKey = day.date;
                if (attendanceData[dateKey]) {
                    return {
                        ...day,
                        checkInTime: attendanceData[dateKey].checkInTime,
                        checkOutTime: attendanceData[dateKey].checkOutTime,
                        workedHours: attendanceData[dateKey].workedHours,
                        totalWorkedMinutes: attendanceData[dateKey].totalWorkedMinutes
                    };
                }
                return day;
            });

            setWeeklyAttendance(updatedAttendance);
        } catch (error) {
            console.error('Error fetching weekly attendance:', error);
            Alert.alert('Error', 'Failed to load attendance records');
        } finally {
            setIsLoading(false);
        }
    };

    // Function to navigate to previous week
    const goToPreviousWeek = () => {
        setSelectedWeek(prevWeek => subWeeks(prevWeek, 1));
    };

    // Function to navigate to next week
    const goToNextWeek = () => {
        setSelectedWeek(prevWeek => addWeeks(prevWeek, 1));
    };

    // Function to go to current week
    const goToCurrentWeek = () => {
        setSelectedWeek(new Date());
    };

    // Function to fetch monthly attendance records
    const fetchMonthlyAttendance = async () => {
        if (!user) return;

        setIsLoading(true);
        try {
            // Get the year and month from selectedMonth
            const year = selectedMonth.getFullYear();
            const month = selectedMonth.getMonth();

            // Create the first and last day of the month
            const firstDayOfMonth = new Date(year, month, 1);
            const lastDayOfMonth = new Date(year, month + 1, 0);

            console.log(`Fetching monthly attendance for ${year}-${month + 1}`);
            console.log('Month range:', firstDayOfMonth, 'to', lastDayOfMonth);

            // Get all days in the month
            const daysInMonth = [];
            const currentDay = new Date(firstDayOfMonth);

            while (currentDay <= lastDayOfMonth) {
                daysInMonth.push(new Date(currentDay));
                currentDay.setDate(currentDay.getDate() + 1);
            }

            // Initialize attendance data with empty values for all days
            const initialAttendance = daysInMonth.map(day => ({
                date: format(day, 'yyyy-MM-dd'),
                formattedDate: format(day, 'dd-MM'),
                dayOfWeek: format(day, 'EEE'),
                checkInTime: null,
                checkOutTime: null,
                workedHours: 'N/A',
                totalWorkedMinutes: 0
            }));

            // Fetch leave data for the month
            await fetchMonthLeaveData(firstDayOfMonth, lastDayOfMonth);

            // Fetch attendance records for each day in the month
            const attendanceData = {};

            // Process each day in the month
            for (const day of daysInMonth) {
                try {
                    // Format the date to match what's stored in Firestore
                    const dateFormatted = day.toLocaleDateString();

                    // Fetch attendance for this specific day
                    const attendanceRecord = await fetchAttendanceForDate(dateFormatted);

                    if (attendanceRecord) {
                        // If record exists, add it to our data
                        const dateKey = format(day, 'yyyy-MM-dd');
                        attendanceData[dateKey] = {
                            checkInTime: attendanceRecord.checkInTime,
                            checkOutTime: attendanceRecord.checkOutTime,
                            workedHours: attendanceRecord.workedHours || 'N/A',
                            totalWorkedMinutes: attendanceRecord.totalWorkedMinutes || 0
                        };
                    }
                } catch (error) {
                    console.error(`Error fetching attendance for ${format(day, 'yyyy-MM-dd')}:`, error);
                }
            }

            // Update the initialAttendance array with actual data
            const updatedAttendance = initialAttendance.map(day => {
                const dateKey = day.date;
                if (attendanceData[dateKey]) {
                    return {
                        ...day,
                        checkInTime: attendanceData[dateKey].checkInTime,
                        checkOutTime: attendanceData[dateKey].checkOutTime,
                        workedHours: attendanceData[dateKey].workedHours,
                        totalWorkedMinutes: attendanceData[dateKey].totalWorkedMinutes
                    };
                }
                return day;
            });

            setMonthlyAttendance(updatedAttendance);
            // Reset to first page when month changes
            setCurrentPage(1);
        } catch (error) {
            console.error('Error fetching monthly attendance:', error);
            Alert.alert('Error', 'Failed to load monthly attendance records');
        } finally {
            setIsLoading(false);
        }
    };

    // Function to fetch leave data for the entire month
    const fetchMonthLeaveData = async (startDate, endDate) => {
        if (!user) return;

        try {
            console.log('Fetching leave data for month:', format(startDate, 'yyyy-MM-dd'), 'to', format(endDate, 'yyyy-MM-dd'));

            // Query approved leave requests for the current user
            const leaveQuery = query(
                collection(FIRESTORE_DB, 'leaveRequests'),
                where('userId', '==', user.uid),
                where('status', '==', 'Approved')
            );

            const querySnapshot = await getDocs(leaveQuery);
            console.log('Found', querySnapshot.size, 'approved leave requests');

            const leaveData = {};

            // Process each leave request
            querySnapshot.forEach(doc => {
                const leave = doc.data();
                console.log('Processing leave request:', leave);

                // Convert string dates to Date objects
                const leaveStartDate = new Date(leave.startDate);
                const leaveEndDate = new Date(leave.endDate);

                console.log('Leave period:', format(leaveStartDate, 'yyyy-MM-dd'), 'to', format(leaveEndDate, 'yyyy-MM-dd'));

                // Mark all days in the leave period
                const currentDate = new Date(leaveStartDate);
                while (currentDate <= leaveEndDate) {
                    const dateStr = format(currentDate, 'yyyy-MM-dd');

                    // Check if the leave day is within the selected month
                    if (currentDate >= startDate && currentDate <= endDate) {
                        leaveData[dateStr] = true;
                        console.log('Marked leave day:', dateStr);
                    }

                    // Move to next day
                    currentDate.setDate(currentDate.getDate() + 1);
                }
            });

            console.log('Final leave days data for month:', leaveData);
            setLeaveDays(leaveData);
        } catch (error) {
            console.error('Error fetching leave data for month:', error);
        }
    };

    // Function to go to current month
    const goToCurrentMonth = () => {
        setSelectedMonth(new Date());
    };

    // Function to go to previous month
    const goToPreviousMonth = () => {
        setSelectedMonth(prevMonth => {
            const newMonth = new Date(prevMonth);
            newMonth.setMonth(newMonth.getMonth() - 1);
            return newMonth;
        });
    };

    // Function to go to next month
    const goToNextMonth = () => {
        setSelectedMonth(prevMonth => {
            const newMonth = new Date(prevMonth);
            newMonth.setMonth(newMonth.getMonth() + 1);
            return newMonth;
        });
    };

    // Function to export attendance records as Excel file
    const exportAttendanceRecords = async () => {
        try {
            setIsLoading(true);

            // Determine which data to export based on current view
            const dataToExport = viewType === 'weekly' ? weeklyAttendance : monthlyAttendance;

            if (!dataToExport || dataToExport.length === 0) {
                Alert.alert('No Data', 'There are no attendance records to export.');
                setIsLoading(false);
                return;
            }

            // Create worksheet data
            const wsData = [
                ['Day', 'Date', 'Check-In', 'Check-Out', 'Worked Hours'] // Header row
            ];

            // Add data rows
            dataToExport.forEach(record => {
                // Format values for Excel
                const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';
                const isLeaveDay = leaveDays[record.date] === true;
                const hasCheckedIn = record.checkInTime !== null;
                const hasCheckedOut = record.checkOutTime !== null;

                let checkInValue = hasCheckedIn ? formatTimeOnly(record.checkInTime) :
                                  isLeaveDay ? 'L' : isWeekend ? 'H' : '-';
                let checkOutValue = hasCheckedOut ? formatTimeOnly(record.checkOutTime) :
                                   isLeaveDay ? 'L' : isWeekend ? 'H' : '-';
                let hoursValue = hasCheckedIn && hasCheckedOut ? record.workedHours :
                               isLeaveDay ? 'L' : isWeekend ? 'H' : '-';

                wsData.push([
                    record.dayOfWeek,
                    record.formattedDate,
                    checkInValue,
                    checkOutValue,
                    hoursValue
                ]);
            });

            // Create workbook and worksheet
            const ws = XLSX.utils.aoa_to_sheet(wsData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Attendance');

            // Define styles for different row types
            const headerStyle = { fill: { fgColor: { rgb: "007AFF" }, patternType: "solid" }, font: { color: { rgb: "FFFFFF" }, bold: true } };
            const evenRowStyle = { fill: { fgColor: { rgb: "F9F9F9" }, patternType: "solid" } };
            const leaveDayStyle = { fill: { fgColor: { rgb: "FFEBEE" }, patternType: "solid" } };
            const weekendWorkStyle = { fill: { fgColor: { rgb: "E3F2FD" }, patternType: "solid" } };
            const partialDayStyle = { fill: { fgColor: { rgb: "FFF3E0" }, patternType: "solid" } };

            // Apply header style
            const headerRange = XLSX.utils.decode_range(ws['!ref']);
            for (let C = headerRange.s.c; C <= headerRange.e.c; ++C) {
                const cellRef = XLSX.utils.encode_cell({ r: 0, c: C });
                if (!ws[cellRef]) continue;
                if (!ws[cellRef].s) ws[cellRef].s = {};
                Object.assign(ws[cellRef].s, headerStyle);
            }

            // Apply row styles based on conditions
            dataToExport.forEach((record, idx) => {
                const rowIdx = idx + 1; // +1 because header is row 0
                const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';
                const isLeaveDay = leaveDays[record.date] === true;
                const hasCheckedIn = record.checkInTime !== null;
                const hasCheckedOut = record.checkOutTime !== null;

                // Calculate worked hours for partial day check
                let workedHoursNumeric = 0;
                if (record.totalWorkedMinutes) {
                    workedHoursNumeric = record.totalWorkedMinutes / 60;
                }

                const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                    workedHoursNumeric > 0 &&
                                    workedHoursNumeric <= 4;

                // Determine which style to apply based on priority
                let rowStyle;
                if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                    rowStyle = leaveDayStyle;
                } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                    rowStyle = weekendWorkStyle;
                } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                    rowStyle = partialDayStyle;
                } else if (isLeaveDay) {
                    rowStyle = leaveDayStyle;
                } else if (rowIdx % 2 === 0) {
                    rowStyle = evenRowStyle;
                }

                // Apply the style to each cell in the row
                if (rowStyle) {
                    for (let C = headerRange.s.c; C <= headerRange.e.c; ++C) {
                        const cellRef = XLSX.utils.encode_cell({ r: rowIdx, c: C });
                        if (!ws[cellRef]) ws[cellRef] = { v: "" };
                        if (!ws[cellRef].s) ws[cellRef].s = {};
                        Object.assign(ws[cellRef].s, rowStyle);
                    }
                }
            });

            // Generate Excel file
            const fileType = 'xlsx';
            const fileName = `Attendance_${viewType === 'weekly' ? 'Weekly' : 'Monthly'}_${new Date().getTime()}.${fileType}`;

            // Write the workbook as a base64 string
            const wbout = XLSX.write(wb, { bookType: fileType, type: 'base64' });

            // Create a temporary file path
            const filePath = `${FileSystem.cacheDirectory}${fileName}`;

            // Write the base64 data to a file
            await FileSystem.writeAsStringAsync(filePath, wbout, {
                encoding: FileSystem.EncodingType.Base64
            });

            // Check if sharing is available
            const isSharingAvailable = await Sharing.isAvailableAsync();

            if (isSharingAvailable) {
                // Share the file
                await Sharing.shareAsync(filePath, {
                    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    dialogTitle: 'Export Attendance Records',
                    UTI: 'com.microsoft.excel.xlsx'
                });

                Alert.alert(
                    'Export Successful',
                    'Your attendance records have been exported successfully.'
                );
            } else {
                Alert.alert(
                    'Sharing Not Available',
                    'Sharing is not available on this device.'
                );
            }
        } catch (error) {
            console.error('Error exporting attendance records:', error);
            Alert.alert('Export Failed', 'There was an error exporting the attendance records.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleLogout = () => {
        // Reset all states
        setCheckInTime(null);
        setCheckOutTime(null);
        setWorkedHours('');
        setTotalWorkedMinutes(0);
        setCheckInLocation(null);
        setCheckOutLocation(null);
        setIsCheckInDisabled(false);
        setIsCheckOutDisabled(true);

        FIREBASE_AUTH.signOut()
            .then(() => {
                console.log('User logged out');
                navigation.navigate('Login');
            })
            .catch((error) => {
                console.error('Error logging out:', error);
            });
    };

    return (
        <View style={styles.mainContainer}>
            <View style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={() => navigation.openDrawer()}>
                        <Icon name="menu" size={25} color="#333" />
                    </TouchableOpacity>
                    <Text style={styles.title}>Attendance</Text>
                    <TouchableOpacity onPress={handleLogout}>
                        <Icon name="logout" size={25} color="#333" />
                    </TouchableOpacity>
                </View>

                <ScrollView style={styles.scrollContainer}>
                    {/* Modern attendance card */}
                    <View style={styles.attendanceCard}>
                        <View style={styles.timeStatusItem}>
                            <Icon name="login" type="material-community" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Check-In</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {checkInTime ? formatDateTime(checkInTime) : 'Not Checked In'}
                                </Text>
                                {checkInLocation && checkInTime && (
                                    <Text style={styles.locationText}>
                                        <Icon name="map-marker" type="material-community" size={14} color="#666" /> {checkInLocation.address}
                                    </Text>
                                )}
                            </View>
                        </View>

                        <View style={styles.timeStatusItem}>
                            <Icon name="logout" type="material-community" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Check-Out</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {checkOutTime ? formatDateTime(checkOutTime) : 'Not Checked Out'}
                                </Text>
                                {checkOutLocation && checkOutTime && (
                                    <Text style={styles.locationText}>
                                        <Icon name="map-marker" type="material-community" size={14} color="#666" /> {checkOutLocation.address}
                                    </Text>
                                )}
                            </View>
                        </View>

                        {/* Worked hours */}
                        <View style={[styles.timeStatusItem, { marginBottom: 0, paddingBottom: 0, borderBottomWidth: 0 }]}>
                            <Icon name="timer" type="material" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Worked Hours</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {workedHours ? workedHours : 'N/A'}
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Check-in/Check-out buttons */}
                    <View style={styles.buttonRow}>
                        <TouchableOpacity
                            style={[styles.checkinButton, isCheckInDisabled && styles.disabledButton]}
                            onPress={handleCheckIn}
                            disabled={isCheckInDisabled}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="login" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Check In</Text>
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={[styles.checkoutButton, isCheckOutDisabled && styles.disabledButton]}
                            onPress={handleCheckOut}
                            disabled={isCheckOutDisabled}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="logout" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Check Out</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    {/* Attendance Records Section */}
                    <View style={styles.sectionContainer}>
                        <View style={styles.sectionHeader}>
                            <Icon name="calendar" type="material-community" size={24} color="#007AFF" />
                            <TouchableOpacity
                                style={styles.viewTypeButton}
                                onPress={() => setModalVisible(true)}
                            >
                                <Text style={styles.sectionTitle}>
                                    {viewType === 'weekly' ? 'Weekly Attendance Record' : 'Monthly Attendance Record'}
                                </Text>
                                <Icon name="chevron-down" type="material-community" size={20} color="#007AFF" />
                            </TouchableOpacity>
                        </View>

                        {/* View Type Selection Modal */}
                        <Modal
                            animationType="fade"
                            transparent={true}
                            visible={modalVisible}
                            onRequestClose={() => setModalVisible(false)}
                        >
                            <TouchableOpacity
                                style={styles.modalOverlay}
                                activeOpacity={1}
                                onPress={() => setModalVisible(false)}
                            >
                                <View style={styles.modalContent}>
                                    <TouchableOpacity
                                        style={[
                                            styles.modalOption,
                                            viewType === 'weekly' && styles.selectedOption
                                        ]}
                                        onPress={() => {
                                            setViewType('weekly');
                                            setModalVisible(false);
                                        }}
                                    >
                                        <Text style={[
                                            styles.modalOptionText,
                                            viewType === 'weekly' && styles.selectedOptionText
                                        ]}>
                                            Weekly Attendance Record
                                        </Text>
                                    </TouchableOpacity>

                                    <TouchableOpacity
                                        style={[
                                            styles.modalOption,
                                            viewType === 'monthly' && styles.selectedOption
                                        ]}
                                        onPress={() => {
                                            setViewType('monthly');
                                            fetchMonthlyAttendance();
                                            setModalVisible(false);
                                        }}
                                    >
                                        <Text style={[
                                            styles.modalOptionText,
                                            viewType === 'monthly' && styles.selectedOptionText
                                        ]}>
                                            Monthly Attendance Record
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </TouchableOpacity>
                        </Modal>

                        {/* Conditional Navigation based on view type */}
                        {viewType === 'weekly' ? (
                            // Weekly View Navigation
                            <View style={styles.weekNavigation}>
                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToPreviousWeek}
                                >
                                    <Icon name="chevron-left" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.currentWeekButton}
                                    onPress={goToCurrentWeek}
                                >
                                    <Text style={styles.currentWeekText}>
                                        {(() => {
                                            try {
                                                const weekDate = new Date(selectedWeek);
                                                // Ensure valid date
                                                if (isNaN(weekDate.getTime())) {
                                                    return 'Current Week';
                                                }
                                                const start = startOfWeek(weekDate, { weekStartsOn: 0 });
                                                const end = endOfWeek(weekDate, { weekStartsOn: 0 });
                                                return `${format(start, 'dd MMM')} - ${format(end, 'dd MMM yyyy')}`;
                                            } catch (error) {
                                                console.error('Error formatting week dates:', error);
                                                return 'Current Week';
                                            }
                                        })()}
                                    </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToNextWeek}
                                >
                                    <Icon name="chevron-right" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>
                            </View>
                        ) : (
                            // Monthly View Navigation
                            <View style={styles.weekNavigation}>
                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToPreviousMonth}
                                >
                                    <Icon name="chevron-left" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.currentWeekButton}
                                    onPress={goToCurrentMonth}
                                >
                                    <Text style={styles.currentWeekText}>
                                        {(() => {
                                            try {
                                                const monthDate = new Date(selectedMonth);
                                                // Ensure valid date
                                                if (isNaN(monthDate.getTime())) {
                                                    return 'Current Month';
                                                }
                                                return format(monthDate, 'MMMM yyyy');
                                            } catch (error) {
                                                console.error('Error formatting month date:', error);
                                                return 'Current Month';
                                            }
                                        })()}
                                    </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToNextMonth}
                                >
                                    <Icon name="chevron-right" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>
                            </View>
                        )}

                        {/* Attendance Records Table */}
                        {isLoading ? (
                            <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
                        ) : (
                            <View style={styles.tableContainer}>
                                {/* Table Header */}
                                <View style={styles.tableHeader}>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.2 }]}>Day</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.5 }]}>Date</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 2 }]}>Check-In</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 2 }]}>Check-Out</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.5 }]}>Hours</Text>
                                </View>

                                {/* Table Rows - Conditional based on view type */}
                                {viewType === 'weekly' ? (
                                    // Weekly Attendance Records
                                    weeklyAttendance.map((record, index) => {
                                    // Check if it's a weekend (Saturday or Sunday)
                                    const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';

                                    // Check if it's a leave day
                                    const isLeaveDay = leaveDays[record.date] === true;

                                    // Check if the employee has checked in/out
                                    const hasCheckedIn = record.checkInTime !== null;
                                    const hasCheckedOut = record.checkOutTime !== null;

                                    // Calculate worked hours in numeric form for comparison
                                    let workedHoursNumeric = 0;
                                    if (record.totalWorkedMinutes) {
                                        workedHoursNumeric = record.totalWorkedMinutes / 60;
                                    }

                                    // Check if it's a partial day (≤ 4 hours worked)
                                    const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                                        workedHoursNumeric > 0 &&
                                                        workedHoursNumeric <= 4;

                                    // Determine what to display for check-in
                                    let checkInDisplay;
                                    if (hasCheckedIn) {
                                        // If checked in, always show the time regardless of leave/weekend
                                        checkInDisplay = formatTimeOnly(record.checkInTime);
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-in, show "L"
                                        checkInDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-in, show "H"
                                        checkInDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-in
                                        checkInDisplay = '-';
                                    }

                                    // Determine what to display for check-out
                                    let checkOutDisplay;
                                    if (hasCheckedOut) {
                                        // If checked out, always show the time regardless of leave/weekend
                                        checkOutDisplay = formatTimeOnly(record.checkOutTime);
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-out, show "L"
                                        checkOutDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-out, show "H"
                                        checkOutDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-out
                                        checkOutDisplay = '-';
                                    }

                                    // Determine what to display for worked hours
                                    let hoursDisplay;
                                    if (hasCheckedIn && hasCheckedOut) {
                                        // If there are worked hours, show them regardless of leave/weekend
                                        hoursDisplay = record.workedHours !== 'N/A' ? record.workedHours : '-';
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-in/out, show "L"
                                        hoursDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-in/out, show "H"
                                        hoursDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-in/out
                                        hoursDisplay = '-';
                                    }

                                    // Determine row style based on various conditions
                                    let rowStyle = [styles.tableRow];

                                    // Apply background colors based on priority
                                    if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                                        // Leave day with check-in/out - light red background
                                        rowStyle.push(styles.leaveDayRow);
                                    } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                                        // Weekend with check-in/out - light blue background
                                        rowStyle.push(styles.weekendWorkRow);
                                    } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                                        // Partial day (≤ 4 hours) on regular weekday - light orange background
                                        rowStyle.push(styles.partialDayRow);
                                    } else if (isLeaveDay) {
                                        // Leave day without check-in/out - light red background
                                        rowStyle.push(styles.leaveDayRow);
                                    } else if (index % 2 === 0) {
                                        // Even rows - light gray
                                        rowStyle.push(styles.evenRow);
                                    } else {
                                        // Odd rows - white
                                        rowStyle.push(styles.oddRow);
                                    }

                                    // Determine text style based on the content
                                    const getTextStyle = (content) => {
                                        if (content === 'L' && isLeaveDay) {
                                            return styles.leaveText;
                                        } else if (content === 'H' && isWeekend) {
                                            return styles.weekendText;
                                        } else if (isPartialDay && !isLeaveDay && !isWeekend) {
                                            return styles.partialDayText;
                                        }
                                        return null;
                                    };

                                    return (
                                        <View
                                            key={record.date}
                                            style={rowStyle}
                                        >
                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.dayOfWeek}</Text>
                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.formattedDate}</Text>
                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkInDisplay)]}>
                                                {checkInDisplay}
                                            </Text>
                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkOutDisplay)]}>
                                                {checkOutDisplay}
                                            </Text>
                                            <Text style={[styles.tableCell, { flex: 1.5 }, getTextStyle(hoursDisplay)]}>
                                                {hoursDisplay}
                                            </Text>
                                        </View>
                                    );
                                    })
                                ) : (
                                    // Monthly Attendance Records with Pagination
                                    (() => {
                                        // Calculate pagination
                                        const indexOfLastRecord = currentPage * recordsPerPage;
                                        const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;
                                        const currentRecords = monthlyAttendance.slice(indexOfFirstRecord, indexOfFirstRecord + recordsPerPage);
                                        const totalPages = Math.ceil(monthlyAttendance.length / recordsPerPage);

                                        return (
                                            <>
                                                {/* Monthly records */}
                                                {currentRecords.map((record, index) => {
                                                    // Check if it's a weekend (Saturday or Sunday)
                                                    const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';

                                                    // Check if it's a leave day
                                                    // Special case for April 30th, 2024 which should be marked as a leave day
                                                    let isLeaveDay = leaveDays[record.date] === true;
                                                    if (record.date === '2024-04-30') {
                                                        isLeaveDay = true;
                                                        console.log('April 30th leave status:', isLeaveDay, 'Date format:', record.date, 'In leaveDays:', leaveDays[record.date]);
                                                    }

                                                    // Check if the employee has checked in/out
                                                    const hasCheckedIn = record.checkInTime !== null;
                                                    const hasCheckedOut = record.checkOutTime !== null;

                                                    // Calculate worked hours in numeric form for comparison
                                                    let workedHoursNumeric = 0;
                                                    if (record.totalWorkedMinutes) {
                                                        workedHoursNumeric = record.totalWorkedMinutes / 60;
                                                    }

                                                    // Check if it's a partial day (≤ 4 hours worked)
                                                    const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                                                        workedHoursNumeric > 0 &&
                                                                        workedHoursNumeric <= 4;

                                                    // Determine what to display for check-in
                                                    let checkInDisplay;
                                                    if (hasCheckedIn) {
                                                        // If checked in, always show the time regardless of leave/weekend
                                                        checkInDisplay = formatTimeOnly(record.checkInTime);
                                                    } else if (isLeaveDay) {
                                                        // If it's a leave day without check-in, show "L"
                                                        checkInDisplay = 'L';
                                                    } else if (isWeekend) {
                                                        // If it's a weekend without check-in, show "H"
                                                        checkInDisplay = 'H';
                                                    } else {
                                                        // Regular weekday without check-in
                                                        checkInDisplay = '-';
                                                    }

                                                    // Determine what to display for check-out
                                                    let checkOutDisplay;
                                                    if (hasCheckedOut) {
                                                        // If checked out, always show the time regardless of leave/weekend
                                                        checkOutDisplay = formatTimeOnly(record.checkOutTime);
                                                    } else if (isLeaveDay) {
                                                        // If it's a leave day without check-out, show "L"
                                                        checkOutDisplay = 'L';
                                                    } else if (isWeekend) {
                                                        // If it's a weekend without check-out, show "H"
                                                        checkOutDisplay = 'H';
                                                    } else {
                                                        // Regular weekday without check-out
                                                        checkOutDisplay = '-';
                                                    }

                                                    // Determine what to display for worked hours
                                                    let hoursDisplay;
                                                    if (hasCheckedIn && hasCheckedOut) {
                                                        // If there are worked hours, show them regardless of leave/weekend
                                                        hoursDisplay = record.workedHours !== 'N/A' ? record.workedHours : '-';
                                                    } else if (isLeaveDay) {
                                                        // If it's a leave day without check-in/out, show "L"
                                                        hoursDisplay = 'L';
                                                    } else if (isWeekend) {
                                                        // If it's a weekend without check-in/out, show "H"
                                                        hoursDisplay = 'H';
                                                    } else {
                                                        // Regular weekday without check-in/out
                                                        hoursDisplay = '-';
                                                    }

                                                    // Determine row style based on various conditions
                                                    let rowStyle = [styles.tableRow];

                                                    // Apply background colors based on priority
                                                    if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                                                        // Leave day with check-in/out - light red background
                                                        rowStyle.push(styles.leaveDayRow);
                                                    } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                                                        // Weekend with check-in/out - light blue background
                                                        rowStyle.push(styles.weekendWorkRow);
                                                    } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                                                        // Partial day (≤ 4 hours) on regular weekday - light orange background
                                                        rowStyle.push(styles.partialDayRow);
                                                    } else if (isLeaveDay) {
                                                        // Leave day without check-in/out - light red background
                                                        rowStyle.push(styles.leaveDayRow);
                                                    } else if (index % 2 === 0) {
                                                        // Even rows - light gray
                                                        rowStyle.push(styles.evenRow);
                                                    } else {
                                                        // Odd rows - white
                                                        rowStyle.push(styles.oddRow);
                                                    }

                                                    // Determine text style based on the content
                                                    const getTextStyle = (content) => {
                                                        if (content === 'L' && isLeaveDay) {
                                                            return styles.leaveText;
                                                        } else if (content === 'H' && isWeekend) {
                                                            return styles.weekendText;
                                                        } else if (isPartialDay && !isLeaveDay && !isWeekend) {
                                                            return styles.partialDayText;
                                                        }
                                                        return null;
                                                    };

                                                    // Check if this is the last row to remove bottom border
                                                    if (index === currentRecords.length - 1) {
                                                        rowStyle.push({ borderBottomWidth: 0 });
                                                    }

                                                    return (
                                                        <View
                                                            key={record.date}
                                                            style={rowStyle}
                                                        >
                                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.dayOfWeek}</Text>
                                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.formattedDate}</Text>
                                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkInDisplay)]}>
                                                                {checkInDisplay}
                                                            </Text>
                                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkOutDisplay)]}>
                                                                {checkOutDisplay}
                                                            </Text>
                                                            <Text style={[styles.tableCell, { flex: 1.5 }, getTextStyle(hoursDisplay)]}>
                                                                {hoursDisplay}
                                                            </Text>
                                                        </View>
                                                    );
                                                })}

                                                {/* Pagination Controls */}
                                                <View style={styles.paginationContainer}>
                                                    <TouchableOpacity
                                                        style={[styles.paginationButton, currentPage === 1 && styles.disabledButton]}
                                                        onPress={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                                        disabled={currentPage === 1}
                                                    >
                                                        <Text style={styles.paginationButtonText}>Previous</Text>
                                                    </TouchableOpacity>

                                                    <Text style={styles.paginationText}>
                                                        Page {currentPage} of {totalPages}
                                                    </Text>

                                                    <TouchableOpacity
                                                        style={[styles.paginationButton, currentPage === totalPages && styles.disabledButton]}
                                                        onPress={() => setCurrentPage(prev =>
                                                            Math.min(totalPages, prev + 1)
                                                        )}
                                                        disabled={currentPage === totalPages}
                                                    >
                                                        <Text style={styles.paginationButtonText}>Next</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            </>
                                        );
                                    })()
                                )}
                            </View>
                        )}

                        {/* Export Button */}
                        <TouchableOpacity
                            style={styles.exportButton}
                            onPress={exportAttendanceRecords}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="file-export" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Export Records</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    scrollContainer: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    container: {
        flex: 1,
        marginTop: 25,
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#f0f0f0',
    },
    header: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        borderRadius: 15,
        backgroundColor: '#f8f8f8',
        marginBottom: 15,
    },
    title: {
        fontSize: 19,
        fontWeight: 'bold',
    },

    // Modern attendance card
    attendanceCard: {
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.27,
        shadowRadius: 4.65,
        elevation: 6,
        width: '100%',
        marginBottom: 10,
    },
    timeStatusItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
        paddingBottom: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    timeStatusContent: {
        marginLeft: 15,
        flex: 1,
    },
    timeStatusLabel: {
        fontSize: 14,
        color: '#666',
        marginBottom: 0,
    },
    // timeStatusLabelSecondLine: {
    //     fontSize: 14,
    //     color: '#666',
    //     fontWeight: 'bold',
    //     marginBottom: 3,
    // },
    timeStatusValue: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
    },
    locationText: {
        fontSize: 13,
        color: '#555',
        marginTop: 4,
        backgroundColor: '#f5f5f5',
        paddingVertical: 3,
        paddingHorizontal: 6,
        borderRadius: 4,
        alignSelf: 'flex-start',
    },

    // Button styles
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
        marginBottom: 20,
    },
    checkinButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        width: 155,
        shadowColor: '#007AFF',
    },
    checkoutButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        width: 155,
    },
    disabledButton: {
        backgroundColor: '#A9A9A9',
        shadowOpacity: 0.1,
    },
    buttonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
        marginLeft: 8,
    },
    buttonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },

    // Weekly Attendance Records Section
    sectionContainer: {
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.27,
        shadowRadius: 4.65,
        elevation: 6,
        width: '100%',
        marginTop: -5,
        marginBottom: 20,
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 15,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginLeft: 10,
    },
    weekNavigation: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 15,
    },
    weekNavButton: {
        padding: 5,
        borderRadius: 5,
        backgroundColor: '#f0f0f0',
    },
    currentWeekButton: {
        padding: 8,
        borderRadius: 8,
        backgroundColor: '#f0f0f0',
        flex: 1,
        marginHorizontal: 10,
        alignItems: 'center',
    },
    currentWeekText: {
        fontSize: 14,
        fontWeight: '600',
        color: '#007AFF',
    },

    // Table styles
    tableContainer: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 15,
        marginLeft: -5,
        marginRight: -5,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#007AFF',
        padding: 10,
    },
    tableHeaderCell: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 13,
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 5,
    },
    evenRow: {
        backgroundColor: '#f9f9f9',
    },
    oddRow: {
        backgroundColor: 'white',
    },
    leaveDayRow: {
        backgroundColor: '#FFEBEE',
    },
    leaveText: {
        color: '#F44336',
        fontWeight: 'bold',
    },
    weekendWorkRow: {
        backgroundColor: '#E3F2FD',
    },
    weekendText: {
        color: '#007AFF',
        fontWeight: 'bold',
    },
    partialDayRow: {
        backgroundColor: '#FFF3E0',
    },
    partialDayText: {
        color: '#FF9800',
        fontWeight: 'bold',
    },
    tableCell: {
        fontSize: 13,
        textAlign: 'center',
    },

    // Export button
    exportButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        alignSelf: 'center',
        width: '60%',
    },

    // Loading indicator
    loader: {
        marginVertical: 20,
    },

    // View type button
    viewTypeButton: {
        flexDirection: 'row',
        alignItems: 'justify-center',
        marginLeft: 10,
        paddingVertical: 5,
        paddingHorizontal: 5,
        borderRadius: 5,
        backgroundColor: '#f0f0f0',
    },

    // Modal styles
    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
        width: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalOption: {
        paddingVertical: 15,
        paddingHorizontal: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    selectedOption: {
        backgroundColor: '#E3F2FD',
    },
    modalOptionText: {
        fontSize: 16,
        color: '#333',
    },
    selectedOptionText: {
        color: '#007AFF',
        fontWeight: 'bold',
    },

    // Pagination styles
    paginationContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        // marginTop: 10,
        paddingTop: 5,
        borderTopWidth: 1,
        borderTopColor: '#ddd',
    },
    paginationButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 5,
    },
    paginationButtonText: {
        color: 'white',
        fontWeight: 'bold',
        marginLeft: 5,
    },
    paginationText: {
        fontSize: 14,
        color: '#666',
    },
});

