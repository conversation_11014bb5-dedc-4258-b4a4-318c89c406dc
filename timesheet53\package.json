{"name": "timesheet53", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-config": "^0.20.14", "@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "expo": "~53.0.0", "expo-router": "^5.0.7", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "firebase": "^10.7.1", "graphql": "^16.8.1", "graphql-tag": "^2.12.6", "react": "19.0.0", "react-native": "0.79.2", "react-native-dotenv": "^3.4.9", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "tslib": "^2.6.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "typescript": "^5.1.3"}, "private": true}