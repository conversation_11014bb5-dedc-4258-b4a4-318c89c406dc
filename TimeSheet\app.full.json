{"expo": {"name": "TimeSheet", "slug": "TimeSheet", "version": "1.0.0", "orientation": "portrait", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "sdkVersion": "53.0.0", "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs access to your location for check-in and check-out functionality."}}, "android": {"adaptiveIcon": {"backgroundColor": "#ffffff"}, "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font", "expo-web-browser", "expo-location"], "experiments": {"typedRoutes": true}}}