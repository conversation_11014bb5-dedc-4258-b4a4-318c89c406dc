SignUp.js - <PERSON><PERSON> K
	    <EMAIL>
	    <PERSON><PERSON>@upvote2

	    <PERSON>
	    <EMAIL>
	    Jane@upvote1


SignUp.js

// import React, { useState } from 'react';
// import { View, Text, TextInput, Button, StyleSheet, Image, TouchableOpacity} from 'react-native';
// import { FIREBASE_AUTH } from '../firebaseConfig';
// import { createUserWithEmailAndPassword, GoogleAuthProvider, signInWithCredential } from 'firebase/auth';
// import { Ionicons } from "@expo/vector-icons";   
// // import { GoogleSignin } from '@react-native-google-signin/google-signin';
// // import * as Font from 'expo-font';

// export default function SignUp({ navigation }) {

//     const [username, setUsername] = useState('');
//     const [email, setEmail] = useState('');
//     const [password, setPassword] = useState('');
//     const [message, setMessage] = useState('');
//     const [messageType, setMessageType] = useState('');
//     const [passwordVisible, setPasswordVisible] = useState(false);

//     const validateEmail = (email) => {
//         const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
//         return emailRegex.test(email);
//     };

//     const validatePassword = (password) => {
//         const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
//         return passwordRegex.test(password);
//     };

//     const handleSignUp = () => {
//         if (username === '') {
//             setMessage('Please enter your username');
//             setMessageType('error');
//             return;
//         }

//         if (!validateEmail(email)) {
//             setMessage('Please provide a valid email address.');
//             setMessageType('error');
//             return;
//         }
    
//         if (!validatePassword(password)) {
//             setMessage('Password must have at least: \n - 1 capital letter \n - 1 small letter \n - 1 number \n - 1 special character \n - 8 characters long password');
//             setMessageType('error');
//             return;
//         }
    
//         createUserWithEmailAndPassword(FIREBASE_AUTH, email, password)
//             .then((userCredential) => {
//                 console.log('You have successfully signed up');
//                 setMessage('You have successfully signed up');
//                 setMessageType('success');
    
//                 // Navigating to Authentication screen
//                 navigation.navigate('Authentication', { email });
//             })
//             .catch((error) => {
//                 if (error.code === 'auth/email-already-in-use') {
//                     setMessage('This email address is already in use. Try signing in or use a different email address.');
//                     setMessageType('error');
//                 } else {
//                     console.error('Error signing up:', error);
//                     setMessage('Provide your email address in proper format');
//                     setMessageType('error');
//                 }
//             });
//     };

//     const handleGoogleSignIn = async () => {
//         try {
//             // Configure Google Sign In
//             GoogleSignin.configure({
//                 webClientId: 'YOUR_WEB_CLIENT_ID', // Get this from Firebase Console
//             });

//             // Get the users ID token
//             const { idToken } = await GoogleSignin.signIn();
            
//             // Create a Google credential with the token
//             const googleCredential = GoogleAuthProvider.credential(idToken);

//             // Sign-in the user with the credential
//             const userCredential = await signInWithCredential(FIREBASE_AUTH, googleCredential);
            
//             console.log('Successfully signed in with Google');
//             setMessage('Successfully signed in with Google');
//             setMessageType('success');
            
//             // Navigate to Authentication screen
//             navigation.navigate('Authentication', { email: userCredential.user.email });
//         } catch (error) {
//             console.error('Google Sign In Error:', error);
//             setMessage('Failed to sign in with Google');
//             setMessageType('error');
//         }
//     };

//     return (
//         <View style={styles.container}>
//             <View style={styles.header}>
//                 <Image
//                     source={require('../../images/UCS logo.png')} 
//                     style={styles.logo}
//                 />
//             </View>
        
//             <Text style={styles.title}>Create an account</Text>

//             {message ? (
//                 <View style={[styles.messageBox, messageType === 'success' ? styles.successBox : styles.errorBox]}>
//                     <Text style={styles.message}>{message}</Text>
//                 </View>
//             ) : null}

//             <TextInput
//                 style={styles.input}
//                 placeholder="Enter Your Username"
//                 value={username}
//                 onChangeText={setUsername}
//             />

//             <TextInput
//                 style={styles.input}
//                 placeholder="Enter Your Email"
//                 value={email}
//                 onChangeText={setEmail}
//             />

//             <View style={styles.passwordContainer}>
//                 <TextInput
//                     style={styles.passwordInput}
//                     placeholder="Enter Your Password"
//                     value={password}
//                     onChangeText={setPassword}
//                     secureTextEntry={!passwordVisible}
//                 />
//                 <TouchableOpacity onPress={() => setPasswordVisible(!passwordVisible)}>
//                     <Ionicons
//                         name={passwordVisible ? 'eye-off' : 'eye'}
//                         size={24}
//                         color="gray"
//                         style={styles.eyeIcon}
//                     />
//                 </TouchableOpacity>
//             </View>

//             <View style={styles.buttonContainer}>
//                 <Button title="Sign Up" onPress={handleSignUp} />
//             </View>
//             <Text style={styles.orText}>________________ Or With ________________</Text>
//             <View style={styles.buttonContainer}>
//                 <Button title="Signup with Google" onPress={() => {}} />
//                     {/* handleGoogleSignIn */}
//             </View>

//             <Text style={styles.footerText}>
//                 Already have an account?
//                 <Text onPress={() => navigation.navigate('Login')} style={styles.loginLink}> Login</Text>
//             </Text>
//         </View>
//     );
// }

// const styles = StyleSheet.create({
//     container: {
//         flex: 1,
//         justifyContent: "flex-start",
//         padding: 16,
//         marginTop: 30,
//     },

//     header: {
//         flexDirection: 'row',
//         alignItems: 'center',
//         marginBottom: 20,
//     },

//     logo: {
//         width: 125,
//         height: 50,
//         marginRight: 10,
//         marginBottom: 45,
//     },

//     title: {
//         fontSize: 24,
//         fontWeight: "bold",
//         fontFamily: "Arial",
//         alignSelf: 'center',
//         marginBottom: 15, 
//     },

//     input: {
//         borderWidth: 1,
//         padding: 8,
//         marginBottom: 26,
//         borderRadius: 10,
//         width: '90%',
//         alignSelf: 'center'
//     },

//     passwordContainer: {
//         flexDirection: 'row',
//         alignItems: 'center',
//         borderWidth: 1,
//         borderRadius: 10,
//         width: '90%',
//         alignSelf: 'center',
//         paddingRight: 10, 
//         marginBottom: 20,
//     },

//     passwordInput: {
//         flex: 1,
//         padding: 10,
//     },

//     eyeIcon: {
//         paddingHorizontal: 5,
//     },

//     buttonContainer: {
//         width: '80%',
//         height: 50,
//         marginBottom: 10,
//         marginTop: 10,
//         justifyContent: 'center',
//         alignSelf: 'center',
//     },

//     messageBox: {
//         padding: 10,
//         borderRadius: 10,
//         marginBottom: 20,
//         alignSelf: 'center',
//         width: '90%',
//     },

//     successBox: {
//         backgroundColor: 'rgba(0, 255, 0, 0.2)', // Translucent green
//     },

//     errorBox: {
//         backgroundColor: 'rgba(255, 0, 0, 0.2)', // Translucent red
//     },

//     message: {
//         textAlign: 'center',
//         color: '#000',
//     },

//     orText: {
//         textAlign: 'center',
//         marginVertical: 16,
//         fontWeight: "bold",
//         fontSize: 15,
//     },

//     footerText: {
//         textAlign: 'center',
//         marginTop: 16,
//     },

//     loginLink: {
//         color: 'darkblue',
//     },
// });

------------------------------------------------------------------------------------------

Tasks.js


import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Modal, TextInput, Alert, ActivityIndicator} from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { collection, query, where, getDocs, updateDoc, deleteDoc, doc, orderBy, serverTimestamp } from 'firebase/firestore';
import { Icon } from 'react-native-elements';
import { format } from 'date-fns';

export default function Tasks({ route, navigation }) {

    const [tasks, setTasks] = useState([]);
    const [loading, setLoading] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingTask, setEditingTask] = useState(null);
    const [editHeading, setEditHeading] = useState('');
    const [editDescription, setEditDescription] = useState('');
    const [editDeadline, setEditDeadline] = useState('');
    const [selectedDate, setSelectedDate] = useState(null);
    const [isAdminView, setIsAdminView] = useState(false);
    const [viewingUserId, setViewingUserId] = useState(null);

    useEffect(() => {
        if (route.params?.date) {
            console.log('New date received:', route.params.date);
            setSelectedDate(route.params.date);
        }
    }, [route.params?.date, route.params?.timestamp]);

    useEffect(() => {
        if (route.params?.isAdminView) {
            setIsAdminView(true);
            setViewingUserId(route.params.userId);
        }
    }, [route.params]);

    useEffect(() => {
        if (selectedDate) {
            console.log('Fetching tasks for date:', selectedDate);
            fetchTasks();
        }
    }, [selectedDate]);

    const fetchTasks = async () => {
        try {
            setLoading(true);
            const user = FIREBASE_AUTH.currentUser;
            
            if (!user) {
                navigation.replace('Login');
                return;
            }
            
            // Determine which user's tasks to fetch
            const targetUserId = isAdminView ? viewingUserId : user.uid;

            if (!isAdminView) {

                const startDate = new Date(selectedDate);
                startDate.setHours(0, 0, 0, 0);
                const endDate = new Date(selectedDate);
                endDate.setHours(23, 59, 59, 999);
                
                console.log('Querying between:', startDate.toISOString(), 'and', endDate.toISOString());
                
                const tasksQuery = query(
                    collection(FIRESTORE_DB, 'tasks'),
                    where('userId', '==', user.uid),
                    where('date', '>=', startDate.toISOString()),
                    where('date', '<=', endDate.toISOString())
                );
            
                const querySnapshot = await getDocs(tasksQuery);
                const tasksList = querySnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
            
                console.log('Tasks found:', tasksList.length);
                setTasks(tasksList);
            }

            if (isAdminView) {
                const tasksQuery = query(
                    collection(FIRESTORE_DB, 'tasks'),
                    where('userId', '==', targetUserId),
                    where('date', '==', selectedDate)
                );
        
                const querySnapshot = await getDocs(tasksQuery);
                const tasksList = querySnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
        
                setTasks(tasksList);
            }

        } catch (error) {
            console.error('Error fetching tasks:', error);
            Alert.alert('Error', 'Failed to fetch tasks');
        } finally {
            setLoading(false);
        }
    };

    const handleEditTask = (task) => {
        setEditingTask(task);
        setEditHeading(task.heading);
        setEditDescription(task.description);
        setEditDeadline(task.deadline);
        setModalVisible(true);
    };

    const handleUpdateTask = async () => {
        try {
            // Form validation
            if (!editHeading.trim()) {
                Alert.alert('Error', 'Please enter a task heading');
                return;
            }
            if (!editDescription.trim()) {
                Alert.alert('Error', 'Please enter a task description');
                return;
            }
            if (!editDeadline.trim()) {
                Alert.alert('Error', 'Please enter a deadline');
                return;
            }

            const taskRef = doc(FIRESTORE_DB, 'tasks', editingTask.id);
            const updateData = {
                heading: editHeading.trim(),
                description: editDescription.trim(),
                deadline: editDeadline.trim(),
                updatedAt: new Date().toISOString()
            };

            await updateDoc(taskRef, updateData);

            // Update local state
            setTasks(tasks.map(task => 
                task.id === editingTask.id 
                    ? { ...task, ...updateData }
                    : task
            ));

            Alert.alert('Success', 'Task updated successfully');
            setModalVisible(false);
            setEditingTask(null);
        } catch (error) {
            console.error('Error updating task:', error);
            Alert.alert('Error', 'Failed to update task');
        }
    };

    const handleDeleteTask = async (taskId) => {
        try {
            Alert.alert(
                'Confirm Delete',
                'Are you sure you want to delete this task?',
                [
                    {
                        text: 'Cancel',
                        style: 'cancel'
                    },
                    {
                        text: 'Delete',
                        style: 'destructive',
                        onPress: async () => {
                            await deleteDoc(doc(FIRESTORE_DB, 'tasks', taskId));
                            setTasks(tasks.filter(task => task.id !== taskId));
                            Alert.alert('Success', 'Task deleted successfully');
                        }
                    }
                ]
            );
        } catch (error) {
            console.error('Error deleting task:', error);
            Alert.alert('Error', 'Failed to delete task');
        }
    };

    const toggleTaskCompletion = async (taskId, currentStatus) => {
        try {
            const taskRef = doc(FIRESTORE_DB, 'tasks', taskId);
            await updateDoc(taskRef, {
                completed: !currentStatus,
                updatedAt: new Date().toISOString()
            });

            // Update local state
            setTasks(tasks.map(task => 
                task.id === taskId 
                    ? { ...task, completed: !currentStatus }
                    : task
            ));
        } catch (error) {
            console.error('Error updating task completion:', error);
            Alert.alert('Error', 'Failed to update task status');
        }
    };

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#007AFF" />
            </View>
        );
    }

    const renderTask = ({ item }) => (
        <View style={styles.taskCard}>
            <View style={styles.taskHeader}>
                <TouchableOpacity 
                    style={styles.checkbox}
                    onPress={() => toggleTaskCompletion(item.id, item.completed)}
                >
                    <Icon 
                        name={item.completed ? "check-box" : "check-box-outline-blank"} 
                        size={18} 
                        color={item.completed ? "#007AFF" : "#666"}
                    />
                </TouchableOpacity>
                <View style={styles.taskHeaderLeft}>
                    <Text style={styles.date}>
                        {format(new Date(item.date), 'dd MMMM, yyyy')}
                    </Text>
                </View>
                <View style={styles.taskActions}>
                    <TouchableOpacity onPress={() => handleEditTask(item)}>
                        <Icon name="edit" size={15} color="#007AFF" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => handleDeleteTask(item.id)}>
                        <Icon name="delete" size={15} color="#FF3B30" />
                    </TouchableOpacity>
                </View>
            </View>
            <View style={styles.taskContent}>
                <Text style={[
                    styles.heading,
                    item.completed && styles.completedText
                ]}>
                    {item.heading}
                </Text>
            </View>
            <Text style={[
                styles.description,
                item.completed && styles.completedText
            ]}>
                {item.description}
            </Text>
            <Text style={styles.deadline}>Deadline: {item.deadline}</Text>
        </View>
    );
    
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.openDrawer()}>
                    <Icon name="menu" size={25} color="#333" />
                </TouchableOpacity>
                <Text style={styles.title}>
                    {selectedDate 
                        ? `Tasks for ${format(new Date(selectedDate), 'dd MMMM, yyyy')}`
                        : 'All Tasks'
                    }
                </Text>
            </View>
            {loading ? (
                <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
            ) : tasks.length === 0 ? (
                <View style={styles.noTasksContainer}>
                    <Text style={styles.noTasksText}>No tasks for this date</Text>
                </View>
            ) : (
                <FlatList
                    data={tasks}
                    renderItem={renderTask}
                    keyExtractor={(item) => item.id}
                    contentContainerStyle={styles.listContainer}
                />
            )}
    
            <Modal
                animationType="slide"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>Edit Task</Text>
                            <TouchableOpacity onPress={() => setModalVisible(false)}>
                                <Icon name="close" size={20} color="black" />
                            </TouchableOpacity>
                        </View>
    
                        <View style={styles.inputContainer}>
                            <Text style={styles.inputLabel}>Task Heading</Text>
                            <TextInput
                                style={styles.input}
                                value={editHeading}
                                onChangeText={setEditHeading}
                            />
    
                            <Text style={styles.inputLabel}>Description</Text>
                            <TextInput
                                style={[styles.input, styles.textArea]}
                                value={editDescription}
                                onChangeText={setEditDescription}
                                multiline={true}
                                numberOfLines={4}
                            />
    
                            <Text style={styles.inputLabel}>Deadline</Text>
                            <TextInput
                                style={styles.input}
                                value={editDeadline}
                                onChangeText={setEditDeadline}
                            />
                        </View>
    
                        <TouchableOpacity 
                            style={styles.saveButton} 
                            onPress={handleUpdateTask}
                        >
                            <Text style={styles.saveButtonText}>Update Task</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        </View>
    );    
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
        backgroundColor: '#f0f0f0',
        marginTop: 25,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 20,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        marginLeft: 20,
        color: '#333',
    },
    listContainer: {
        padding: 8,
    },
    taskCard: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 16,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.23,
        shadowRadius: 2.62,
        elevation: 4,
    },
    date: {
        fontSize: 14,
        color: '#666',
        marginBottom: 8,
    },
    taskContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },   
    heading: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 8,
        color: '#333',
    },
    description: {
        fontSize: 16,
        color: '#444',
        marginBottom: 8,
    },
    deadline: {
        fontSize: 14,
        color: '#666',
        fontStyle: 'italic',
    },
    taskHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    taskActions: {
        flexDirection: 'row',
        gap: 10,
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContainer: {
        width: '90%',
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },
    inputContainer: {
        marginBottom: 20,
    },
    inputLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: '#666',
        marginBottom: 5,
    },
    input: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        marginBottom: 15,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
    },
    textArea: {
        height: 100,
        textAlignVertical: 'top',
    },
    saveButton: {
        backgroundColor: '#007AFF',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
    },
    saveButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
    },
    taskHeaderLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
    },
    checkbox: {
        padding: 5,
    },
    completedText: {
        textDecorationLine: 'line-through',
        color: '#999',
    },
    loader: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    noTasksContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    noTasksText: {
        fontSize: 16,
        color: '#666',
    },
});

---------------------------------------------------------------------------------------------------


rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is admin
    function isAdmin() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Users collection rules
    match /users/{userId} {
      allow read: if request.auth != null && (request.auth.uid == userId || isAdmin());
      allow write: if request.auth != null && request.auth.uid == userId;
      allow create: if request.auth != null;
    }
    
    // Tasks collection rules
    match /tasks/{taskId} {
      allow read: if request.auth != null && 
        (resource.data.userId == request.auth.uid || isAdmin());
      allow write: if request.auth != null && 
        (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if request.auth != null;
    }
  }
}

-------------------------------------------------------------------------------------------------------

Login.js

import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, TouchableOpacity } from 'react-native';
import { FIREBASE_AUTH } from '../firebaseConfig';
import { signInWithEmailAndPassword, sendEmailVerification } from 'firebase/auth';
import { Ionicons } from '@expo/vector-icons'; 

export default function Login({ navigation }) {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [message, setMessage] = useState('');
    const [messageType, setMessageType] = useState('');
    const [passwordVisible, setPasswordVisible] = useState(false);

    const handleLogin = () => {
        signInWithEmailAndPassword(FIREBASE_AUTH, email, password)
            .then((userCredential) => {
                const user = userCredential.user;

                if (user.emailVerified) {
                    console.log('User is successfully logged in');
                    setMessage('You have successfully logged in');
                    setMessageType('success');
                    navigation.navigate('Dashboard');
                } else {
                    setMessage('Please verify your email address before logging in.');
                    setMessageType('error');
                    sendEmailVerification(user);
                }
            })
            .catch((error) => {
                if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
                    setMessage('Error logging in: User not found');
                    setMessageType('error');
                } else {
                    setMessage('Email or Password is incorrect. Ensure email is in the correct format and password is entered correctly');
                    setMessageType('error');
                }
            });
    };

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Sign In</Text>

            {message ? (
                <View style={[styles.messageBox, messageType === 'success' ? styles.successBox : styles.errorBox]}>
                    <Text style={styles.message}>{message}</Text>
                </View>
            ) : null}

            <TextInput
                style={styles.input}
                placeholder="Enter Your Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
            />

            <View style={styles.passwordContainer}>
                <TextInput
                    style={styles.passwordInput}
                    placeholder="Enter Your Password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={!passwordVisible}
                />
                <TouchableOpacity onPress={() => setPasswordVisible(!passwordVisible)}>
                    <Ionicons
                        name={passwordVisible ? 'eye-off' : 'eye'}
                        size={24}
                        color="gray"
                        style={styles.eyeIcon}
                    />
                </TouchableOpacity>
            </View>

            <View style={styles.buttonContainer}>
                <Button title="Log In" onPress={handleLogin} />
            </View>

            <Text style={styles.footerText}>
                Don't have an account yet?
                <Text onPress={() => navigation.navigate('SignUp')} style={styles.signUpLink}> Sign Up</Text>
            </Text>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginTop: 25,
        alignItems: 'center',
        padding: 16,
    },

    title: {
        fontSize: 24,
        marginBottom: 50,
        textAlign: 'center',
        fontWeight: 'bold',
        fontFamily: 'Arial',
        marginTop: 50,
    },

    input: {
        borderWidth: 1,
        padding: 10,
        marginBottom: 20,
        borderRadius: 10,
        width: '90%',
        alignSelf: 'center',
    },

    passwordContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 10,
        width: '90%',
        alignSelf: 'center',
        paddingRight: 10, 
        marginBottom: 20,
    },

    passwordInput: {
        flex: 1,
        padding: 10,
    },

    eyeIcon: {
        paddingHorizontal: 5,
    },

    buttonContainer: {
        width: '80%',
        height: 50,
        marginBottom: 10,
        marginTop: 10,
        justifyContent: 'center',
        alignSelf: 'center',
    },

    messageBox: {
        padding: 10,
        borderRadius: 10,
        marginBottom: 20,
        alignSelf: 'center',
        width: '90%',
    },
  
    successBox: {
        backgroundColor: 'rgba(0, 255, 0, 0.2)',
    },
  
    errorBox: {
        backgroundColor: 'rgba(255, 0, 0, 0.2)',
    },
  
    message: {
        textAlign: 'center',
        color: '#000',
    },

    footerText: {
        textAlign: 'center',
        marginTop: 16,
    },

    signUpLink: {
        color: 'darkblue',
    },
});

-----------------------------------------------------------------------------------------------------------

Tasks.js

// import React, { useState, useEffect } from 'react';
// import { View, Text, StyleSheet, FlatList, TouchableOpacity, Modal, TextInput, Alert, ActivityIndicator} from 'react-native';
// import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
// import { collection, query, where, getDocs, updateDoc, deleteDoc, doc, orderBy, serverTimestamp } from 'firebase/firestore';
// import { Icon } from 'react-native-elements';
// import { format } from 'date-fns';

// export default function Tasks({ route, navigation }) {

//     const [tasks, setTasks] = useState([]);
//     const [loading, setLoading] = useState(true);
//     const [modalVisible, setModalVisible] = useState(false);
//     const [editingTask, setEditingTask] = useState(null);
//     const [editHeading, setEditHeading] = useState('');
//     const [editDescription, setEditDescription] = useState('');
//     const [editDeadline, setEditDeadline] = useState('');
//     const [selectedDate, setSelectedDate] = useState(null);
//     const [isAdminView, setIsAdminView] = useState(false);
//     const [viewingUserId, setViewingUserId] = useState(null);
//     const [viewingUsername, setViewingUsername] = useState(null);

//     useEffect(() => {
//         if (route.params?.date) {
//             setSelectedDate(route.params.date);
//         }
//         if (route.params?.isAdminView) {
//             setIsAdminView(true);
//             setViewingUserId(route.params.userId);
//             setViewingUsername(route.params.username);
//         }
//     }, [route.params]);

//     useEffect(() => {
//         if (selectedDate && (viewingUserId || !isAdminView)) {
//             fetchTasks();
//         }
//     }, [selectedDate, viewingUserId, isAdminView]);

//     const fetchTasks = async () => {
//         try {
//             setLoading(true);
//             const user = FIREBASE_AUTH.currentUser;
            
//             if (!user) {
//                 navigation.replace('Login');
//                 return;
//             }
            
//             // Determine which user's tasks to fetch
//             const targetUserId = isAdminView ? viewingUserId : user.uid;

//             const startDate = new Date(selectedDate);
//             startDate.setHours(0, 0, 0, 0);
//             const endDate = new Date(selectedDate);
//             endDate.setHours(23, 59, 59, 999);
            
//             console.log('Fetching tasks for:', {
//                 userId: targetUserId,
//                 date: selectedDate,
//                 isAdminView: isAdminView
//             });
            
//             const tasksQuery = query(
//                 collection(FIRESTORE_DB, 'tasks'),
//                 where('userId', '==', targetUserId),
//                 where('date', '>=', startDate.toISOString()),
//                 where('date', '<=', endDate.toISOString())
//             );
        
//             const querySnapshot = await getDocs(tasksQuery);
//             const tasksList = querySnapshot.docs.map(doc => ({
//                 id: doc.id,
//                 ...doc.data()
//             }));
        
//             console.log('Tasks found:', tasksList.length);
//             setTasks(tasksList);
//         } catch (error) {
//             console.error('Error fetching tasks:', error);
//             Alert.alert('Error', 'Failed to fetch tasks');
//         } finally {
//             setLoading(false);
//         }
//     };

//     const handleEditTask = (task) => {
//         setEditingTask(task);
//         setEditHeading(task.heading);
//         setEditDescription(task.description);
//         setEditDeadline(task.deadline);
//         setModalVisible(true);
//     };

//     const handleUpdateTask = async () => {
//         try {
//             // Form validation
//             if (!editHeading.trim()) {
//                 Alert.alert('Error', 'Please enter a task heading');
//                 return;
//             }
//             if (!editDescription.trim()) {
//                 Alert.alert('Error', 'Please enter a task description');
//                 return;
//             }
//             if (!editDeadline.trim()) {
//                 Alert.alert('Error', 'Please enter a deadline');
//                 return;
//             }

//             const taskRef = doc(FIRESTORE_DB, 'tasks', editingTask.id);
//             const updateData = {
//                 heading: editHeading.trim(),
//                 description: editDescription.trim(),
//                 deadline: editDeadline.trim(),
//                 updatedAt: new Date().toISOString()
//             };

//             await updateDoc(taskRef, updateData);

//             // Update local state
//             setTasks(tasks.map(task => 
//                 task.id === editingTask.id 
//                     ? { ...task, ...updateData }
//                     : task
//             ));

//             Alert.alert('Success', 'Task updated successfully');
//             setModalVisible(false);
//             setEditingTask(null);
//         } catch (error) {
//             console.error('Error updating task:', error);
//             Alert.alert('Error', 'Failed to update task');
//         }
//     };

//     const handleDeleteTask = async (taskId) => {
//         try {
//             Alert.alert(
//                 'Confirm Delete',
//                 'Are you sure you want to delete this task?',
//                 [
//                     {
//                         text: 'Cancel',
//                         style: 'cancel'
//                     },
//                     {
//                         text: 'Delete',
//                         style: 'destructive',
//                         onPress: async () => {
//                             await deleteDoc(doc(FIRESTORE_DB, 'tasks', taskId));
//                             setTasks(tasks.filter(task => task.id !== taskId));
//                             Alert.alert('Success', 'Task deleted successfully');
//                         }
//                     }
//                 ]
//             );
//         } catch (error) {
//             console.error('Error deleting task:', error);
//             Alert.alert('Error', 'Failed to delete task');
//         }
//     };

//     const toggleTaskCompletion = async (taskId, currentStatus) => {
//         try {
//             const taskRef = doc(FIRESTORE_DB, 'tasks', taskId);
//             await updateDoc(taskRef, {
//                 completed: !currentStatus,
//                 updatedAt: new Date().toISOString()
//             });

//             // Update local state
//             setTasks(tasks.map(task => 
//                 task.id === taskId 
//                     ? { ...task, completed: !currentStatus }
//                     : task
//             ));
//         } catch (error) {
//             console.error('Error updating task completion:', error);
//             Alert.alert('Error', 'Failed to update task status');
//         }
//     };

//     if (loading) {
//         return (
//             <View style={styles.loadingContainer}>
//                 <ActivityIndicator size="large" color="#007AFF" />
//             </View>
//         );
//     }

//     const renderTask = ({ item }) => (
//         <View style={styles.taskCard}>
//             <View style={styles.taskHeader}>
//                 <TouchableOpacity 
//                     style={styles.checkbox}
//                     onPress={() => toggleTaskCompletion(item.id, item.completed)}
//                 >
//                     <Icon 
//                         name={item.completed ? "check-box" : "check-box-outline-blank"} 
//                         size={18} 
//                         color={item.completed ? "#007AFF" : "#666"}
//                     />
//                 </TouchableOpacity>
//                 <View style={styles.taskHeaderLeft}>
//                     <Text style={styles.date}>
//                         {format(new Date(item.date), 'dd/MM/yyyy')}
//                     </Text>
//                 </View>
//                 <View style={styles.taskActions}>
//                     <TouchableOpacity onPress={() => handleEditTask(item)}>
//                         <Icon name="edit" size={15} color="#007AFF" />
//                     </TouchableOpacity>
//                     <TouchableOpacity onPress={() => handleDeleteTask(item.id)}>
//                         <Icon name="delete" size={15} color="#FF3B30" />
//                     </TouchableOpacity>
//                 </View>
//             </View>
//             <View style={styles.taskContent}>
//                 <Text style={[
//                     styles.heading,
//                     item.completed && styles.completedText
//                 ]}>
//                     {item.heading}
//                 </Text>
//             </View>
//             <Text style={[
//                 styles.description,
//                 item.completed && styles.completedText
//             ]}>
//                 {item.description}
//             </Text>
//             <Text style={styles.deadline}>Deadline: {item.deadline}</Text>
//         </View>
//     );
    
//     return (
//         <View style={styles.container}>
//             <View style={styles.header}>
//                 {!isAdminView && (
//                     <TouchableOpacity onPress={() => navigation.openDrawer()}>
//                         <Icon name="menu" size={22} color="#333" />
//                     </TouchableOpacity>
//                 )}
//                 <Text style={styles.title}>
//                     {isAdminView 
//                         ? `${viewingUsername}'s Tasks - ${format(new Date(selectedDate), 'dd/MM/yyyy')}`
//                         : `Tasks for ${format(new Date(selectedDate), 'dd/MM/yyyy')}`
//                     }
//                 </Text>
//             </View>
//             {loading ? (
//                 <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
//             ) : tasks.length === 0 ? (
//                 <View style={styles.noTasksContainer}>
//                     <Text style={styles.noTasksText}>No tasks for this date</Text>
//                 </View>
//             ) : (
//                 <FlatList
//                     data={tasks}
//                     renderItem={renderTask}
//                     keyExtractor={(item) => item.id}
//                     contentContainerStyle={styles.listContainer}
//                 />
//             )}
    
//             <Modal
//                 animationType="slide"
//                 transparent={true}
//                 visible={modalVisible}
//                 onRequestClose={() => setModalVisible(false)}
//             >
//                 <View style={styles.modalOverlay}>
//                     <View style={styles.modalContainer}>
//                         <View style={styles.modalHeader}>
//                             <Text style={styles.modalTitle}>Edit Task</Text>
//                             <TouchableOpacity onPress={() => setModalVisible(false)}>
//                                 <Icon name="close" size={20} color="black" />
//                             </TouchableOpacity>
//                         </View>
    
//                         <View style={styles.inputContainer}>
//                             <Text style={styles.inputLabel}>Task Heading</Text>
//                             <TextInput
//                                 style={styles.input}
//                                 value={editHeading}
//                                 onChangeText={setEditHeading}
//                             />
    
//                             <Text style={styles.inputLabel}>Description</Text>
//                             <TextInput
//                                 style={[styles.input, styles.textArea]}
//                                 value={editDescription}
//                                 onChangeText={setEditDescription}
//                                 multiline={true}
//                                 numberOfLines={4}
//                             />
    
//                             <Text style={styles.inputLabel}>Deadline</Text>
//                             <TextInput
//                                 style={styles.input}
//                                 value={editDeadline}
//                                 onChangeText={setEditDeadline}
//                             />
//                         </View>
    
//                         <TouchableOpacity 
//                             style={styles.saveButton} 
//                             onPress={handleUpdateTask}
//                         >
//                             <Text style={styles.saveButtonText}>Update Task</Text>
//                         </TouchableOpacity>
//                     </View>
//                 </View>
//             </Modal>
//         </View>
//     );    
// }

// const styles = StyleSheet.create({
//     container: {
//         flex: 1,
//         padding: 16,
//         backgroundColor: '#f0f0f0',
//         marginTop: 25,
//     },
//     header: {
//         flexDirection: 'row',
//         alignItems: 'center',
//         padding: 20,
//     },
//     title: {
//         fontSize: 20,
//         fontWeight: 'bold',
//         marginLeft: 20,
//         color: '#333',
//     },
//     listContainer: {
//         padding: 8,
//     },
//     taskCard: {
//         backgroundColor: 'white',
//         borderRadius: 10,
//         padding: 16,
//         marginBottom: 16,
//         shadowColor: '#000',
//         shadowOffset: {
//             width: 0,
//             height: 2,
//         },
//         shadowOpacity: 0.23,
//         shadowRadius: 2.62,
//         elevation: 4,
//     },
//     date: {
//         fontSize: 14,
//         color: '#666',
//         marginBottom: 8,
//     },
//     taskContent: {
//         flexDirection: 'row',
//         alignItems: 'center',
//     },   
//     heading: {
//         fontSize: 18,
//         fontWeight: 'bold',
//         marginBottom: 8,
//         color: '#333',
//     },
//     description: {
//         fontSize: 16,
//         color: '#444',
//         marginBottom: 8,
//     },
//     deadline: {
//         fontSize: 14,
//         color: '#666',
//         fontStyle: 'italic',
//     },
//     taskHeader: {
//         flexDirection: 'row',
//         justifyContent: 'space-between',
//         alignItems: 'center',
//         marginBottom: 8,
//     },
//     taskActions: {
//         flexDirection: 'row',
//         gap: 10,
//     },
//     modalOverlay: {
//         flex: 1,
//         justifyContent: 'center',
//         alignItems: 'center',
//         backgroundColor: 'rgba(0, 0, 0, 0.5)',
//     },
//     modalContainer: {
//         width: '90%',
//         backgroundColor: 'white',
//         borderRadius: 15,
//         padding: 20,
//         shadowColor: '#000',
//         shadowOffset: {
//             width: 0,
//             height: 2,
//         },
//         shadowOpacity: 0.25,
//         shadowRadius: 3.84,
//         elevation: 5,
//     },
//     modalHeader: {
//         flexDirection: 'row',
//         justifyContent: 'space-between',
//         alignItems: 'center',
//         marginBottom: 20,
//     },
//     modalTitle: {
//         fontSize: 20,
//         fontWeight: 'bold',
//         color: '#333',
//     },
//     inputContainer: {
//         marginBottom: 20,
//     },
//     inputLabel: {
//         fontSize: 16,
//         fontWeight: '600',
//         color: '#666',
//         marginBottom: 5,
//     },
//     input: {
//         borderWidth: 1,
//         borderColor: '#ddd',
//         borderRadius: 8,
//         padding: 12,
//         marginBottom: 15,
//         fontSize: 16,
//         backgroundColor: '#f9f9f9',
//     },
//     textArea: {
//         height: 100,
//         textAlignVertical: 'top',
//     },
//     saveButton: {
//         backgroundColor: '#007AFF',
//         padding: 15,
//         borderRadius: 8,
//         alignItems: 'center',
//     },
//     saveButtonText: {
//         color: 'white',
//         fontSize: 16,
//         fontWeight: '600',
//     },
//     loadingContainer: {
//         flex: 1,
//         justifyContent: 'center',
//         alignItems: 'center',
//         backgroundColor: '#f0f0f0',
//     },
//     taskHeaderLeft: {
//         flexDirection: 'row',
//         alignItems: 'center',
//         gap: 10,
//     },
//     checkbox: {
//         padding: 5,
//     },
//     completedText: {
//         textDecorationLine: 'line-through',
//         color: '#999',
//     },
//     loader: {
//         flex: 1,
//         justifyContent: 'center',
//         alignItems: 'center',
//     },
//     noTasksContainer: {
//         flex: 1,
//         justifyContent: 'center',
//         alignItems: 'center',
//     },
//     noTasksText: {
//         fontSize: 16,
//         color: '#666',
//     },
// });

//------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Modal, TextInput, Alert, ActivityIndicator} from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { collection, query, where, getDocs, updateDoc, deleteDoc, doc, orderBy, serverTimestamp } from 'firebase/firestore';
import { Icon } from 'react-native-elements';
import { format } from 'date-fns';

export default function Tasks({ route, navigation }) {

    const [tasks, setTasks] = useState([]);
    const [loading, setLoading] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingTask, setEditingTask] = useState(null);
    const [editHeading, setEditHeading] = useState('');
    const [editDescription, setEditDescription] = useState('');
    const [editDeadline, setEditDeadline] = useState('');
    const [selectedDate, setSelectedDate] = useState(null);
    const [isAdminView, setIsAdminView] = useState(false);
    const [viewingUserId, setViewingUserId] = useState(null);
    const [viewingUsername, setViewingUsername] = useState(null);

    useEffect(() => {
        if (route.params?.date) {
            setSelectedDate(route.params.date);
        }
        if (route.params?.isAdminView) {
            setIsAdminView(true);
            setViewingUserId(route.params.userId);
            setViewingUsername(route.params.username);
        }
    }, [route.params]);

    useEffect(() => {
        if (selectedDate && (viewingUserId || !isAdminView)) {
            fetchTasks();
        }
    }, [selectedDate, viewingUserId, isAdminView]);

    const fetchTasks = async () => {
        try {
            setLoading(true);
            const user = FIREBASE_AUTH.currentUser;
            
            if (!user) {
                navigation.replace('Login');
                return;
            }
            
            // Determine which user's tasks to fetch
            const targetUserId = isAdminView ? viewingUserId : user.uid;

            const startDate = new Date(selectedDate);
            startDate.setHours(0, 0, 0, 0);
            const endDate = new Date(selectedDate);
            endDate.setHours(23, 59, 59, 999);
            
            console.log('Fetching tasks for:', {
                userId: targetUserId,
                date: selectedDate,
                isAdminView: isAdminView
            });
            
            const tasksQuery = query(
                collection(FIRESTORE_DB, 'tasks'),
                where('userId', '==', targetUserId),
                where('date', '>=', startDate.toISOString()),
                where('date', '<=', endDate.toISOString())
            );
        
            const querySnapshot = await getDocs(tasksQuery);
            const tasksList = querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        
            console.log('Tasks found:', tasksList.length);
            setTasks(tasksList);
        } catch (error) {
            console.error('Error fetching tasks:', error);
            Alert.alert('Error', 'Failed to fetch tasks');
        } finally {
            setLoading(false);
        }
    };

    const handleAddTask = () => {
        setEditingTask(null);
        setEditHeading('');
        setEditDescription('');
        setEditDeadline('');
        setModalVisible(true);
    };

    const handleSaveTask = async () => {
        try {
            if (!editHeading.trim() || !editDescription.trim() || !editDeadline.trim()) {
                Alert.alert('Error', 'All fields are required');
                return;
            }

            const user = FIREBASE_AUTH.currentUser;
            if (!user) {
                Alert.alert('Error', 'No user logged in');
                return;
            }

            const newTask = {
                userId: isAdminView ? viewingUserId : user.uid,
                heading: editHeading.trim(),
                description: editDescription.trim(),
                deadline: editDeadline.trim(),
                date: selectedDate,
                completed: false,
                createdAt: new Date().toISOString(),
            };

            const docRef = await addDoc(collection(FIRESTORE_DB, 'tasks'), newTask);
            setTasks([...tasks, { id: docRef.id, ...newTask }]);

            Alert.alert('Success', 'Task added successfully');
            setModalVisible(false);
        } catch (error) {
            console.error('Error saving task:', error);
            Alert.alert('Error', 'Failed to save task');
        }
    };

    const handleEditTask = (task) => {
        setEditingTask(task);
        setEditHeading(task.heading);
        setEditDescription(task.description);
        setEditDeadline(task.deadline);
        setModalVisible(true);
    };

    const handleUpdateTask = async () => {
        try {
            // Form validation
            if (!editHeading.trim()) {
                Alert.alert('Error', 'Please enter a task heading');
                return;
            }
            if (!editDescription.trim()) {
                Alert.alert('Error', 'Please enter a task description');
                return;
            }
            if (!editDeadline.trim()) {
                Alert.alert('Error', 'Please enter a deadline');
                return;
            }

            const taskRef = doc(FIRESTORE_DB, 'tasks', editingTask.id);
            const updateData = {
                heading: editHeading.trim(),
                description: editDescription.trim(),
                deadline: editDeadline.trim(),
                updatedAt: new Date().toISOString()
            };

            await updateDoc(taskRef, updateData);

            // Update local state
            setTasks(tasks.map(task => 
                task.id === editingTask.id 
                    ? { ...task, ...updateData }
                    : task
            ));

            Alert.alert('Success', 'Task updated successfully');
            setModalVisible(false);
            setEditingTask(null);
        } catch (error) {
            console.error('Error updating task:', error);
            Alert.alert('Error', 'Failed to update task');
        }
    };

    const handleDeleteTask = async (taskId) => {
        try {
            Alert.alert(
                'Confirm Delete',
                'Are you sure you want to delete this task?',
                [
                    {
                        text: 'Cancel',
                        style: 'cancel'
                    },
                    {
                        text: 'Delete',
                        style: 'destructive',
                        onPress: async () => {
                            await deleteDoc(doc(FIRESTORE_DB, 'tasks', taskId));
                            setTasks(tasks.filter(task => task.id !== taskId));
                            Alert.alert('Success', 'Task deleted successfully');
                        }
                    }
                ]
            );
        } catch (error) {
            console.error('Error deleting task:', error);
            Alert.alert('Error', 'Failed to delete task');
        }
    };

    const toggleTaskCompletion = async (taskId, currentStatus) => {
        try {
            const taskRef = doc(FIRESTORE_DB, 'tasks', taskId);
            await updateDoc(taskRef, {
                completed: !currentStatus,
                updatedAt: new Date().toISOString()
            });

            // Update local state
            setTasks(tasks.map(task => 
                task.id === taskId 
                    ? { ...task, completed: !currentStatus }
                    : task
            ));
        } catch (error) {
            console.error('Error updating task completion:', error);
            Alert.alert('Error', 'Failed to update task status');
        }
    };

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#007AFF" />
            </View>
        );
    }

    const renderTask = ({ item }) => (
        <View style={styles.taskCard}>
            <View style={styles.taskHeader}>
                <TouchableOpacity 
                    style={styles.checkbox}
                    onPress={() => toggleTaskCompletion(item.id, item.completed)}
                >
                    <Icon 
                        name={item.completed ? "check-box" : "check-box-outline-blank"} 
                        size={18} 
                        color={item.completed ? "#007AFF" : "#666"}
                    />
                </TouchableOpacity>
                <View style={styles.taskHeaderLeft}>
                    <Text style={styles.date}>
                        {format(new Date(item.date), 'dd/MM/yyyy')}
                    </Text>
                </View>
                <View style={styles.taskActions}>
                    <TouchableOpacity onPress={() => handleEditTask(item)}>
                        <Icon name="edit" size={15} color="#007AFF" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => handleDeleteTask(item.id)}>
                        <Icon name="delete" size={15} color="#FF3B30" />
                    </TouchableOpacity>
                </View>
            </View>
            <View style={styles.taskContent}>
                <Text style={[
                    styles.heading,
                    item.completed && styles.completedText
                ]}>
                    {item.heading}
                </Text>
            </View>
            <Text style={[
                styles.description,
                item.completed && styles.completedText
            ]}>
                {item.description}
            </Text>
            <Text style={styles.deadline}>Deadline: {item.deadline}</Text>
        </View>
    );
    
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                {!isAdminView && (
                    <TouchableOpacity onPress={() => navigation.openDrawer()}>
                        <Icon name="menu" size={22} color="#333" />
                    </TouchableOpacity>
                )}
                <Text style={styles.title}>
                    {isAdminView 
                        ? `${viewingUsername}'s Tasks - ${format(new Date(selectedDate), 'dd/MM/yyyy')}`
                        : `Tasks for ${format(new Date(selectedDate), 'dd/MM/yyyy')}`
                    }
                </Text>
            </View>
            {loading ? (
                <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
            ) : tasks.length === 0 ? (
                <View style={styles.noTasksContainer}>
                    <Text style={styles.noTasksText}>No tasks for this date</Text>
                </View>
            ) : (
                <FlatList
                    data={tasks}
                    renderItem={renderTask}
                    keyExtractor={(item) => item.id}
                    contentContainerStyle={styles.listContainer}
                />
            )}

            {/* Floating "+" Button */}
            <TouchableOpacity style={styles.addButton} onPress={handleAddTask}>
                <Icon name="add" size={30} color="white" />
            </TouchableOpacity>
    
            <Modal
                animationType="slide"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>Edit Task</Text>
                            <TouchableOpacity onPress={() => setModalVisible(false)}>
                                <Icon name="close" size={20} color="black" />
                            </TouchableOpacity>
                        </View>
    
                        <View style={styles.inputContainer}>
                            <Text style={styles.inputLabel}>Task Heading</Text>
                            <TextInput
                                style={styles.input}
                                value={editHeading}
                                onChangeText={setEditHeading}
                            />
    
                            <Text style={styles.inputLabel}>Description</Text>
                            <TextInput
                                style={[styles.input, styles.textArea]}
                                value={editDescription}
                                onChangeText={setEditDescription}
                                multiline={true}
                                numberOfLines={4}
                            />
    
                            <Text style={styles.inputLabel}>Deadline</Text>
                            <TextInput
                                style={styles.input}
                                value={editDeadline}
                                onChangeText={setEditDeadline}
                            />
                        </View>
    
                        <TouchableOpacity 
                            style={styles.saveButton} 
                            onPress={handleUpdateTask}
                        >
                            <Text style={styles.saveButtonText}>Update Task</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        </View>
    );    
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
        backgroundColor: '#f0f0f0',
        marginTop: 25,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 20,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        marginLeft: 20,
        color: '#333',
    },
    listContainer: {
        padding: 8,
    },
    taskCard: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 16,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.23,
        shadowRadius: 2.62,
        elevation: 4,
    },
    date: {
        fontSize: 14,
        color: '#666',
        marginBottom: 8,
    },
    taskContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },   
    heading: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 8,
        color: '#333',
    },
    description: {
        fontSize: 16,
        color: '#444',
        marginBottom: 8,
    },
    deadline: {
        fontSize: 14,
        color: '#666',
        fontStyle: 'italic',
    },
    taskHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    taskActions: {
        flexDirection: 'row',
        gap: 10,
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContainer: {
        width: '90%',
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },
    inputContainer: {
        marginBottom: 20,
    },
    inputLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: '#666',
        marginBottom: 5,
    },
    input: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        marginBottom: 15,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
    },
    textArea: {
        height: 100,
        textAlignVertical: 'top',
    },
    saveButton: {
        backgroundColor: '#007AFF',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
    },
    saveButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
    },
    taskHeaderLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
    },
    checkbox: {
        padding: 5,
    },
    completedText: {
        textDecorationLine: 'line-through',
        color: '#999',
    },
    loader: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    noTasksContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    noTasksText: {
        fontSize: 16,
        color: '#666',
    },
    addButton: {
            position: 'absolute',
            bottom: 20,
            right: 20,
            backgroundColor: '#007AFF',
            width: 60,
            height: 60,
            borderRadius: 30,
            justifyContent: 'center',
            alignItems: 'center',
            elevation: 5,
    },
});

----------------------------------------------------------------------------------------------------

Dashboard.js

import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, Button, StyleSheet, TouchableOpacity, Modal, Alert} from 'react-native';
import { Calendar } from 'react-native-calendars';
import { Picker } from '@react-native-picker/picker';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { collection, addDoc, serverTimestamp, getDoc, doc } from 'firebase/firestore';
import { Icon } from 'react-native-elements';
import Ionicons from '@expo/vector-icons/Ionicons';

const months = [
    { label: 'January', value: 1 },
    { label: 'February', value: 2 },
    { label: 'March', value: 3 },
    { label: 'April', value: 4 },
    { label: 'May', value: 5 },
    { label: 'June', value: 6 },
    { label: 'July', value: 7 },
    { label: 'August', value: 8 },
    { label: 'September', value: 9 },
    { label: 'October', value: 10 },
    { label: 'November', value: 11 },
    { label: 'December', value: 12 },
];

export default function Dashboard({ navigation }) {
    const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1); 
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
    const [selectedDate, setSelectedDate] = useState('');
    const [modalVisible, setModalVisible] = useState(false);
    const [taskHeading, setTaskHeading] = useState('');
    const [taskDescription, setTaskDescription] = useState('');
    const [taskDeadline, setTaskDeadline] = useState('');
    const [username, setUsername] = useState('');

    useEffect(() => {
        const fetchUserData = async () => {
            const user = FIREBASE_AUTH.currentUser;
            if (user) {
                const userDoc = await getDoc(doc(FIRESTORE_DB, 'users', user.uid));
                if (userDoc.exists()) {
                    setUsername(userDoc.data().username);
                } else {
                    setUsername('User');
                }
            } else {
                setUsername('Guest');
            }
        };

        fetchUserData();
    }, []);

    const handleMonthChange = (itemValue) => {
        setSelectedMonth(itemValue);
    };

    const handleYearChange = (itemValue) => {
        setSelectedYear(itemValue);
    };

    const getCurrentMonthDays = () => {
        const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();
        const markedDates = {};
        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(selectedYear, selectedMonth - 1, day).toISOString().split('T')[0];
            markedDates[date] = { marked: false }; // have to customize more
        }
        return markedDates;
    };

    const handleDayPress = (day) => {
        setSelectedDate(day.dateString);
        setModalVisible(true);
    };

    const handleSaveTask = async () => {
        try {
            // Form validation
            if (!taskHeading.trim()) {
                Alert.alert('error', 'Please enter a task heading');
                return;
            }
            if (!taskDescription.trim()) {
                Alert.alert('error', 'Please enter a task description');
                return;
            }
            if (!taskDeadline.trim()) {
                Alert.alert('error', 'Please enter a deadline');
                return;
            }
    
            const user = FIREBASE_AUTH.currentUser;
            if (!user) {
                Alert.alert('error', 'No user logged in');
                return;
            }
    
            const newTask = {
                userId: user.uid,
                email: user.email,
                username: username,
                date: new Date(selectedDate).toISOString(),
                heading: taskHeading.trim(),
                description: taskDescription.trim(),
                deadline: taskDeadline.trim(),
                completed: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            // Add task to Firestore
            const docRef = await addDoc(collection(FIRESTORE_DB, 'tasks'), newTask);
            
            // Show success message
            Alert.alert('Success', 'Task added successfully');
            
            // Navigate to Tasks screen with both date and new task
            navigation.navigate('Tasks', { 
                date: selectedDate,
                newTask: { id: docRef.id, ...newTask } 
            });
            
            // Reset form and close modal
            setTaskHeading('');
            setTaskDescription('');
            setTaskDeadline('');
            setModalVisible(false);
        } catch (error) {
            console.error('Error saving task:', error);
            Alert.alert('Error', 'Failed to save task. Please try again.');
        }
    };

    const viewTasksForDate = (date) => {
        navigation.navigate('Tasks', { 
            date: date,
            timestamp: new Date().getTime()
        });
    };

    const getMonthName = (monthNumber) => {
        const monthIndex = monthNumber - 1;
        return months[monthIndex].label;
    };

    const handleLogout = () => {
        FIREBASE_AUTH.signOut()
            .then(() => {
                console.log('User logged out');
                navigation.navigate('Login');
            })
            .catch((error) => {
                console.error('Error logging out:', error);
            });
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.openDrawer()}>
                    <Icon name="menu" size={25} color="#333" />
                </TouchableOpacity>
                <Text style={styles.title}>Dashboard</Text>
                <TouchableOpacity onPress={handleLogout}>
                    <Icon name="logout" size={25} color="#333" />
                </TouchableOpacity>
            </View>

            <View>
                <Text style={[styles.usertext, { textAlign: 'left' }]}>Hello, {username}!</Text>
            </View>

            <View style={styles.pickerContainer}>
                <Picker
                    selectedValue={selectedMonth}
                    style={styles.picker}
                    onValueChange={handleMonthChange}
                >
                    {months.map((month) => (
                        <Picker.Item key={month.value} label={month.label} value={month.value} />
                    ))}
                </Picker>
                <Picker
                    selectedValue={selectedYear}
                    style={styles.picker}
                    onValueChange={handleYearChange}
                >
                    {[...Array(20).keys()].map(year => (
                        <Picker.Item key={year} label={`${selectedYear - 10 + year}`} value={selectedYear - 10 + year} />
                    ))}
                </Picker>
            </View>
            <View style={styles.card}>
                <Calendar
                    key={`${selectedYear}-${selectedMonth}`} 
                    style={styles.calendar}
                    hideArrows={true}
                    theme={{
                        calendarBackground: 'white',
                        textSectionTitleColor: '#b6c1cd',
                        selectedDayBackgroundColor: '#00adf5',
                        selectedDayTextColor: '#ffffff',
                        todayTextColor: '#00adf5',
                        dayTextColor: '#2d4150',
                        textDisabledColor: '#d9e1e8',
                        dotColor: '#00adf5',
                        selectedDotColor: '#ffffff',
                        arrowColor: 'orange',
                        monthTextColor: 'black',
                        indicatorColor: 'black',
                        textDayFontWeight: '300',
                        textMonthFontWeight: 'bold',
                        textDayHeaderFontWeight: '300',
                        textDayFontSize: 16,
                        textMonthFontSize: 16,
                        textDayHeaderFontSize: 14,
                    }}
                    markedDates={getCurrentMonthDays()}
                    current={`${selectedYear}-${selectedMonth.toString().padStart(2, '0')}-01`} 
                    onDayPress={(day) => {
                        setSelectedDate(day.dateString);
                        Alert.alert(
                            'Select Option',
                            `What would you like to do for ${day.dateString}?`,
                            [
                                {
                                    text: 'Add Task',
                                    onPress: () => setModalVisible(true)
                                },
                                {
                                    text: 'View Tasks',
                                    onPress: () => viewTasksForDate(day.dateString)
                                },
                                {
                                    text: 'Cancel',
                                    style: 'cancel'
                                }
                            ]
                        );
                    }}
                />
            </View>
            <Modal
                animationType="slide"
                presentationStyle='pageSheet'
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}> Add Task for {selectedDate}</Text>
                            <TouchableOpacity 
                                onPress={() => setModalVisible(false)} 
                                style={styles.closeButton}
                            >
                                <Ionicons name="close" size={24} color="black" />
                            </TouchableOpacity>
                        </View>
                        
                        <View style={styles.inputContainer}>
                            <Text style={styles.inputLabel}>Task Heading</Text>
                            <TextInput
                                style={styles.input}
                                placeholder="Enter task heading"
                                value={taskHeading}
                                onChangeText={setTaskHeading}
                            />
                            
                            <Text style={styles.inputLabel}>Description</Text>
                            <TextInput
                                style={[styles.input, styles.textArea]}
                                placeholder="Enter task description"
                                value={taskDescription}
                                onChangeText={setTaskDescription}
                                multiline={true}
                                numberOfLines={4}
                            />
                            
                            <Text style={styles.inputLabel}>Deadline</Text>
                            <TextInput
                                style={styles.input}
                                placeholder="Enter deadline (e.g. 5:00 PM)"
                                value={taskDeadline}
                                onChangeText={setTaskDeadline}
                            />
                        </View>

                        <TouchableOpacity 
                            style={styles.saveButton} 
                            onPress={handleSaveTask}
                        >
                            <Text style={styles.saveButtonText}>Save Task</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginTop: 25,
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#f0f0f0',
    },
    header: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        backgroundColor: '#f8f8f8',
        marginBottom: 40,
    },
    title: {
        fontSize: 19,
        fontWeight: 'bold',
    },

    pickerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: 10,
    },

    picker: {
        height: 55,
        width: 160,
    },

    card: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        width: '90%',
    },

    calendar: {
        borderRadius: 10,
    },

    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },

    modalContainer: {
        width: '90%',
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },

    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
    },

    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },

    closeButton: {
        padding: 5,
    },

    inputContainer: {
        marginBottom: 20,
    },

    inputLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: '#666',
        marginBottom: 5,
    },

    input: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        marginBottom: 15,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
    },

    textArea: {
        height: 100,
        textAlignVertical: 'top',
    },

    saveButton: {
        backgroundColor: '#007AFF',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
    },
    
    saveButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
    usertext: {
        textAlign: 'justify',
        fontStyle: 'italic',
        fontSize: 20,
        fontWeight: '600',
        color: '#333',
        marginBottom: 20,
    },
});

-------------------------------------------------------------------------------------

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Modal, TextInput, Alert, ActivityIndicator} from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { collection, query, where, getDoc, getDocs, addDoc, updateDoc, deleteDoc, doc, orderBy, serverTimestamp } from 'firebase/firestore';
import { Icon } from 'react-native-elements';
import { format } from 'date-fns';

export default function Tasks({ route, navigation }) {

    const [tasks, setTasks] = useState([]);
    const [loading, setLoading] = useState(true);
    
    // separate modals for adding and editing tasks
    const [modalAddVisible, setModalAddVisible] = useState(false);
    const [modalEditVisible, setModalEditVisible] = useState(false);

    // task state
    const [editingTask, setEditingTask] = useState( null);
    const [editHeading, setEditHeading] = useState('');
    const [editDescription, setEditDescription] = useState('');
    const [editDeadline, setEditDeadline] = useState('');

    // date and admin view
    const [selectedDate, setSelectedDate] = useState(null);
    const [isAdminView, setIsAdminView] = useState(false);
    const [viewingUserId, setViewingUserId] = useState(null);
    const [viewingUsername, setViewingUsername] = useState(null);

    // role
    const [userRole, setUserRole] = useState(null);

    useEffect(() => {
        const fetchUserRole = async () => {
            const user = FIREBASE_AUTH.currentUser;
            if (user) {
                const userDoc = await getDoc(doc(FIRESTORE_DB, "users", user.uid));
                if (userDoc.exists()) {
                    setUserRole(userDoc.data().role);
                }
            }
        };
        fetchUserRole();
    }, []);

    const showMenu = !(isAdminView || userRole === "admin");
    const user = FIREBASE_AUTH.currentUser;

    useEffect(() => {
        if (route.params?.date) {
            setSelectedDate(route.params.date);
        }
        if (route.params?.isAdminView) {
            setIsAdminView(true);
            setViewingUserId(route.params.userId || FIREBASE_AUTH.currentUser?.uid);
            setViewingUsername(route.params.username);
        }
    }, [route.params]);
    

    useEffect(() => {
        if (selectedDate && (viewingUserId || !isAdminView)) {
            fetchTasks();
        }
    }, [selectedDate, viewingUserId, isAdminView]);

    const fetchTasks = async () => {
        try {
            setLoading(true);
            const user = FIREBASE_AUTH.currentUser;
            if (!user) {
                navigation.replace('Login');
                return;
            }
            
            // Determine which user's tasks to fetch
            const targetUserId = isAdminView ? viewingUserId : user.uid;

            const startDate = new Date(selectedDate);
            startDate.setHours(0, 0, 0, 0);
            const endDate = new Date(selectedDate);
            endDate.setHours(23, 59, 59, 999);
            
            console.log('Fetching tasks for:', {
                userId: targetUserId,
                date: selectedDate,
                isAdminView: isAdminView
            }, viewingUserId);
            
            const tasksQuery = query(
                collection(FIRESTORE_DB, 'tasks'),
                where('userId', '==', targetUserId),
                where('date', '>=', startDate.toISOString()),
                where('date', '<=', endDate.toISOString())
            );
        
            const querySnapshot = await getDocs(tasksQuery);
            const tasksList = querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
            console.log('Tasks found:', tasksList.length);
            setTasks(tasksList);
        } catch (error) {
            console.error('Error fetching tasks:', error);
            Alert.alert('Error', 'Failed to fetch tasks');
        } finally {
            setLoading(false);
        }
    };

    const handleAddTask = () => {
        setEditingTask(null);
        setEditHeading('');
        setEditDescription('');
        setEditDeadline('');
        setModalAddVisible(true);
    };

    const handleSaveTask = async () => {
        try {
            if (!editHeading.trim() || !editDescription.trim() || !editDeadline.trim()) {
                Alert.alert('Error', 'All fields are required');
                return;
            }
    
            const user = FIREBASE_AUTH.currentUser;
            if (!user) {
                Alert.alert('Error', 'No user logged in');
                return;
            }
    
            // Fetch the username from Firestore or use a default value
            let username = 'User';
            try {
                const userDoc = await getDoc(doc(FIRESTORE_DB, 'users', user.uid));
                if (userDoc.exists()) {
                    username = userDoc.data().username;
                }
            } catch (error) {
                console.error('Error fetching username:', error);
            }
    
            const newTask = {
                userId: isAdminView ? viewingUserId : user.uid,
                email: user.email,
                username: username,
                heading: editHeading.trim(),
                description: editDescription.trim(),
                deadline: editDeadline.trim(),
                date: selectedDate,
                completed: false,
                createdAt: new Date().toISOString(),
                createdBy: isAdminView ? "admin" : user.uid,
            };
    
            const docRef = await addDoc(collection(FIRESTORE_DB, 'tasks'), newTask);
            setTasks([...tasks, { id: docRef.id, ...newTask }]);
    
            Alert.alert('Success', 'Task added successfully');
            setModalAddVisible(false);
        } catch (error) {
            console.error('Error saving task:', error);
            Alert.alert('Error', 'Failed to save task');
        }
    };

    const handleEditTask = (task) => {
        setEditingTask(task);
        setEditHeading(task.heading);
        setEditDescription(task.description);
        setEditDeadline(task.deadline);
        setModalEditVisible(true);
    };

    const handleUpdateTask = async () => {
        try {
            // Form validation
            if (!editHeading.trim()) {
                Alert.alert('Error', 'Please enter a task heading');
                return;
            }
            if (!editDescription.trim()) {
                Alert.alert('Error', 'Please enter a task description');
                return;
            }
            if (!editDeadline.trim()) {
                Alert.alert('Error', 'Please enter a deadline');
                return;
            }

            const taskRef = doc(FIRESTORE_DB, 'tasks', editingTask.id);
            const updateData = {
                heading: editHeading.trim(),
                description: editDescription.trim(),
                deadline: editDeadline.trim(),
                updatedAt: new Date().toISOString()
            };

            await updateDoc(taskRef, updateData);

            // Update local state
            setTasks(tasks.map(task => 
                task.id === editingTask.id 
                    ? { ...task, ...updateData }
                    : task
            ));

            Alert.alert('Success', 'Task updated successfully');
            setModalEditVisible(false);
            setEditingTask(null);
        } catch (error) {
            console.error('Error updating task:', error);
            Alert.alert('Error', 'Failed to update task');
        }
    };

    const handleDeleteTask = async (taskId) => {
        try {
            Alert.alert(
                'Confirm Delete',
                'Are you sure you want to delete this task?',
                [
                    {
                        text: 'Cancel',
                        style: 'cancel'
                    },
                    {
                        text: 'Delete',
                        style: 'destructive',
                        onPress: async () => {
                            await deleteDoc(doc(FIRESTORE_DB, 'tasks', taskId));
                            setTasks(tasks.filter(task => task.id !== taskId));
                            Alert.alert('Success', 'Task deleted successfully');
                        }
                    }
                ]
            );
        } catch (error) {
            console.error('Error deleting task:', error);
            Alert.alert('Error', 'Failed to delete task');
        }
    };

    const toggleTaskCompletion = async (taskId, currentStatus) => {
        try {
            const taskRef = doc(FIRESTORE_DB, 'tasks', taskId);
            await updateDoc(taskRef, {
                completed: !currentStatus,
                updatedAt: new Date().toISOString()
            });

            // Update local state
            setTasks(tasks.map(task => 
                task.id === taskId 
                    ? { ...task, completed: !currentStatus }
                    : task
            ));
        } catch (error) {
            console.error('Error updating task completion:', error);
            Alert.alert('Error', 'Failed to update task status');
        }
    };

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#007AFF" />
            </View>
        );
    }

    const renderTask = ({ item }) => {
        const user = FIREBASE_AUTH.currentUser;
        const isAdminCreated = item.createdBy === "admin"; 
        const isCurrentUserAdmin = isAdminView;
        const isAdminViewUserTasks = isAdminView && viewingUserId !== user?.uid;

        return (
            <View style={styles.taskCard}>
            <View style={styles.taskHeader}>

                {/* admin viewing user tasks, task status (dot) else check-box */}
                {isAdminViewUserTasks ? (
                    <View style={styles.statusContainer}>
                        <Icon 
                            name="circle" 
                            type="font-awesome" 
                            size={14} 
                            color={item.completed ? "green" : "#FFDB58"} 
                            style={styles.statusDot}
                        />
                        <Text style={styles.statusText}>
                            {item.completed ? "Completed" : "In-progress"}
                        </Text>
                    </View>
                ) : (
                    <TouchableOpacity 
                        style={styles.checkbox}
                        onPress={() => toggleTaskCompletion(item.id, item.completed)}
                    >
                        <Icon 
                            name={item.completed ? "check-box" : "check-box-outline-blank"} 
                            size={18} 
                            color={item.completed ? "#007AFF" : "#666"}
                        />
                    </TouchableOpacity>
                )}

                <View style={styles.taskHeaderLeft}>
                    <Text style={styles.date}>
                        {format(new Date(item.date), 'dd/MM/yyyy')}
                    </Text>
                </View>

                <View style={styles.taskActions}>
                    {/* disable edit & delete if task is from Admin for users*/}
                    <TouchableOpacity 
                        onPress={() => !item.completed && (isCurrentUserAdmin || !isAdminCreated) && handleEditTask(item)}
                        disabled={item.completed || (!isCurrentUserAdmin && isAdminCreated)}
                    >
                        <Icon name="edit" size={15} color={(item.completed || (!isCurrentUserAdmin && isAdminCreated)) ? "#ccc" : "#007AFF"} />
                    </TouchableOpacity>

                    <TouchableOpacity 
                        onPress={() => (isCurrentUserAdmin || !isAdminCreated) && handleDeleteTask(item.id)}
                        disabled={!isCurrentUserAdmin && isAdminCreated}
                    >
                        <Icon name="delete" size={15} color={(!isCurrentUserAdmin && isAdminCreated) ? "#ccc" : "#FF3B30"} />
                    </TouchableOpacity>
                </View>
            </View>

            <View style={styles.taskContent}>
                <Text style={[
                    styles.heading,
                    item.completed && styles.completedText
                ]}>
                    {item.heading}
                </Text>
            </View>
            <Text style={[
                styles.description,
                item.completed && styles.completedText
            ]}>
                {item.description}
            </Text>
            <Text style={styles.deadline}>Deadline: {item.deadline}</Text>
        </View>
    );
}
    
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                {/* {showMenu && (
                    <TouchableOpacity onPress={() => navigation.openDrawer()}>
                        <Icon name="menu" size={22} color="#333" />
                    </TouchableOpacity>
                )} */}

                {/* back arrow for admin and users in tasks */}
                {isAdminView ? (
                    <TouchableOpacity 
                        onPress={() => navigation.navigate(
                            isAdminView && viewingUserId === user.uid
                                ? "Admin"
                                : "AdminTasks" 
                        )}
                        style={styles.backButton}  
                    >
                        <Icon name="arrow-left" type="feather" size={20} color="#333" />
                    </TouchableOpacity>
                ) : showMenu && (
                    <TouchableOpacity onPress={() => navigation.openDrawer()}>
                        <Icon name="menu" size={22} color="#333" />
                    </TouchableOpacity>
                )}

                <Text style={styles.title}>
                    {isAdminView 
                        ? `${viewingUsername}'s Tasks - ${format(new Date(selectedDate), 'dd/MM/yyyy')}`
                        : `Tasks for ${format(new Date(selectedDate), 'dd/MM/yyyy')}`
                    }
                </Text>
            </View>
            {loading ? (
                <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
            ) : tasks.length === 0 ? (
                <View style={styles.noTasksContainer}>
                    <Text style={styles.noTasksText}>No tasks for this date</Text>
                </View>
            ) : (
                <FlatList
                data={tasks}
                renderItem={renderTask}
                keyExtractor={(item) => item.id}
                />
            )}

            {/* floating "+" button */}
            <TouchableOpacity style={styles.addButton} onPress={handleAddTask}>
                <Icon name="add" size={30} color="white" />
            </TouchableOpacity>
    
           {/* add task modal */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={modalAddVisible}
                onRequestClose={() => setModalAddVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>Add Task for {selectedDate}</Text>
                            <TouchableOpacity onPress={() => setModalAddVisible(false)}>
                                <Icon name="close" size={20} color="black" />
                            </TouchableOpacity>
                        </View>
    
                        <View style={styles.inputContainer}>
                            <Text style={styles.inputLabel}>Task Heading</Text>
                            <TextInput
                                style={styles.input}
                                value={editHeading}
                                onChangeText={setEditHeading}
                            />
    
                            <Text style={styles.inputLabel}>Description</Text>
                            <TextInput
                                style={[styles.input, styles.textArea]}
                                value={editDescription}
                                onChangeText={setEditDescription}
                                multiline={true}
                                numberOfLines={4}
                            />
    
                            <Text style={styles.inputLabel}>Deadline</Text>
                            <TextInput
                                style={styles.input}
                                value={editDeadline}
                                onChangeText={setEditDeadline}
                            />
                        </View>
    
                        <TouchableOpacity 
                            style={styles.saveButton} 
                            onPress={handleSaveTask}
                        >
                            <Text style={styles.saveButtonText}>Save Task</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

            {/* edit task modal */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={modalEditVisible}
                onRequestClose={() => setModalEditVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>Add Task for {selectedDate}</Text>
                            <TouchableOpacity onPress={() => setModalEditVisible(false)}>
                                <Icon name="close" size={20} color="black" />
                            </TouchableOpacity>
                        </View>
    
                        <View style={styles.inputContainer}>
                            <Text style={styles.inputLabel}>Task Heading</Text>
                            <TextInput
                                style={styles.input}
                                value={editHeading}
                                onChangeText={setEditHeading}
                            />
    
                            <Text style={styles.inputLabel}>Description</Text>
                            <TextInput
                                style={[styles.input, styles.textArea]}
                                value={editDescription}
                                onChangeText={setEditDescription}
                                multiline={true}
                                numberOfLines={4}
                            />
    
                            <Text style={styles.inputLabel}>Deadline</Text>
                            <TextInput
                                style={styles.input}
                                value={editDeadline}
                                onChangeText={setEditDeadline}
                            />
                        </View>
    
                        <TouchableOpacity 
                            style={styles.saveButton} 
                            onPress={handleUpdateTask}
                        >
                            <Text style={styles.saveButtonText}>Update Task</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        </View>
    );    
}

what wrong in the code, while the admin is looking at their own tasks, then nothing is appearing on the left top as I am not able to see any back button

error
 (NOBRIDGE) WARN  Found screens with the same name nested inside one another. Check:

Admin, Admin > Admin,
Dashboard, Dashboard > Dashboard

This can cause confusing behavior during navigation. Consider using unique names for each screen instead. [Component Stack]

//-------------------------------------------------------------------------------------------------------------------------------------------------

Tasks.js

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Modal, TextInput, Alert, ActivityIndicator} from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { collection, query, where, getDoc, getDocs, addDoc, updateDoc, deleteDoc, doc, orderBy, serverTimestamp } from 'firebase/firestore';
import { Icon } from 'react-native-elements';
import { format } from 'date-fns';

export default function Tasks({ route, navigation }) {

    const [tasks, setTasks] = useState([]);
    const [loading, setLoading] = useState(true);
    
    // separate modals for adding and editing tasks
    const [modalAddVisible, setModalAddVisible] = useState(false);
    const [modalEditVisible, setModalEditVisible] = useState(false);

    // task state
    const [editingTask, setEditingTask] = useState( null);
    const [editHeading, setEditHeading] = useState('');
    const [editDescription, setEditDescription] = useState('');
    const [editDeadline, setEditDeadline] = useState('');

    // date and admin view
    const [selectedDate, setSelectedDate] = useState(null);
    const [isAdminView, setIsAdminView] = useState(false);
    const [viewingUserId, setViewingUserId] = useState(null);
    const [viewingUsername, setViewingUsername] = useState(null);

    // role
    const [userRole, setUserRole] = useState(null);

    useEffect(() => {
        const fetchUserRole = async () => {
            const user = FIREBASE_AUTH.currentUser;
            if (user) {
                const userDoc = await getDoc(doc(FIRESTORE_DB, "users", user.uid));
                if (userDoc.exists()) {
                    setUserRole(userDoc.data().role);
                }
            }
        };
        fetchUserRole();
    }, []);

    const showMenu = !(isAdminView || userRole === "admin");
    const user = FIREBASE_AUTH.currentUser;

    useEffect(() => {
        if (route.params?.date) {
            setSelectedDate(route.params.date);
        }
        if (route.params?.isAdminView) {
            setIsAdminView(true);
            setViewingUserId(route.params.userId);
            setViewingUsername(route.params.username);
        }
    }, [route.params]);
    

    useEffect(() => {
        if (selectedDate && (viewingUserId || !isAdminView)) {
            fetchTasks();
        }
    }, [selectedDate, viewingUserId, isAdminView]);

    const fetchTasks = async () => {
        try {
            setLoading(true);
            const user = FIREBASE_AUTH.currentUser;
            if (!user) {
                navigation.replace('Login');
                return;
            }
            
            // Determine which user's tasks to fetch
            const targetUserId = isAdminView ? viewingUserId : user.uid;

            const startDate = new Date(selectedDate);
            startDate.setHours(0, 0, 0, 0);
            const endDate = new Date(selectedDate);
            endDate.setHours(23, 59, 59, 999);
            
            console.log('Fetching tasks for:', {
                userId: targetUserId,
                date: selectedDate,
                isAdminView: isAdminView
            }, targetUserId, viewingUserId);
            
            const tasksQuery = query(
                collection(FIRESTORE_DB, 'tasks'),
                where('userId', '==', targetUserId),
                where('date', '>=', startDate.toISOString()),
                where('date', '<=', endDate.toISOString())
            );
        
            const querySnapshot = await getDocs(tasksQuery);
            const tasksList = querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
            console.log('Tasks found:', tasksList.length);
            setTasks(tasksList);
        } catch (error) {
            console.error('Error fetching tasks:', error);
            Alert.alert('Error', 'Failed to fetch tasks');
        } finally {
            setLoading(false);
        }
    };

    const handleAddTask = () => {
        setEditingTask(null);
        setEditHeading('');
        setEditDescription('');
        setEditDeadline('');
        setModalAddVisible(true);
    };

    const handleSaveTask = async () => {
        try {
            if (!editHeading.trim() || !editDescription.trim() || !editDeadline.trim()) {
                Alert.alert('Error', 'All fields are required');
                return;
            }
    
            const user = FIREBASE_AUTH.currentUser;
            if (!user) {
                Alert.alert('Error', 'No user logged in');
                return;
            }
    
            // Fetch the username from Firestore or use a default value
            let username = 'User';
            try {
                const userDoc = await getDoc(doc(FIRESTORE_DB, 'users', user.uid));
                if (userDoc.exists()) {
                    username = userDoc.data().username;
                }
            } catch (error) {
                console.error('Error fetching username:', error);
            }
    
            const newTask = {
                userId: isAdminView ? viewingUserId : user.uid,
                email: user.email,
                username: username,
                heading: editHeading.trim(),
                description: editDescription.trim(),
                deadline: editDeadline.trim(),
                date: selectedDate,
                completed: false,
                createdAt: new Date().toISOString(),
                createdBy: isAdminView ? "admin" : user.uid,
            };
    
            const docRef = await addDoc(collection(FIRESTORE_DB, 'tasks'), newTask);
            setTasks([...tasks, { id: docRef.id, ...newTask }]);
    
            Alert.alert('Success', 'Task added successfully');
            setModalAddVisible(false);
        } catch (error) {
            console.error('Error saving task:', error);
            Alert.alert('Error', 'Failed to save task');
        }
    };

    const handleEditTask = (task) => {
        setEditingTask(task);
        setEditHeading(task.heading);
        setEditDescription(task.description);
        setEditDeadline(task.deadline);
        setModalEditVisible(true);
    };

    const handleUpdateTask = async () => {
        try {
            // Form validation
            if (!editHeading.trim()) {
                Alert.alert('Error', 'Please enter a task heading');
                return;
            }
            if (!editDescription.trim()) {
                Alert.alert('Error', 'Please enter a task description');
                return;
            }
            if (!editDeadline.trim()) {
                Alert.alert('Error', 'Please enter a deadline');
                return;
            }

            const taskRef = doc(FIRESTORE_DB, 'tasks', editingTask.id);
            const updateData = {
                heading: editHeading.trim(),
                description: editDescription.trim(),
                deadline: editDeadline.trim(),
                updatedAt: new Date().toISOString()
            };

            await updateDoc(taskRef, updateData);

            // Update local state
            setTasks(tasks.map(task => 
                task.id === editingTask.id 
                    ? { ...task, ...updateData }
                    : task
            ));

            Alert.alert('Success', 'Task updated successfully');
            setModalEditVisible(false);
            setEditingTask(null);
        } catch (error) {
            console.error('Error updating task:', error);
            Alert.alert('Error', 'Failed to update task');
        }
    };

    const handleDeleteTask = async (taskId) => {
        try {
            Alert.alert(
                'Confirm Delete',
                'Are you sure you want to delete this task?',
                [
                    {
                        text: 'Cancel',
                        style: 'cancel'
                    },
                    {
                        text: 'Delete',
                        style: 'destructive',
                        onPress: async () => {
                            await deleteDoc(doc(FIRESTORE_DB, 'tasks', taskId));
                            setTasks(tasks.filter(task => task.id !== taskId));
                            Alert.alert('Success', 'Task deleted successfully');
                        }
                    }
                ]
            );
        } catch (error) {
            console.error('Error deleting task:', error);
            Alert.alert('Error', 'Failed to delete task');
        }
    };

    const toggleTaskCompletion = async (taskId, currentStatus) => {
        try {
            const taskRef = doc(FIRESTORE_DB, 'tasks', taskId);
            await updateDoc(taskRef, {
                completed: !currentStatus,
                updatedAt: new Date().toISOString()
            });

            // Update local state
            setTasks(tasks.map(task => 
                task.id === taskId 
                    ? { ...task, completed: !currentStatus }
                    : task
            ));
        } catch (error) {
            console.error('Error updating task completion:', error);
            Alert.alert('Error', 'Failed to update task status');
        }
    };

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#007AFF" />
            </View>
        );
    }

    const renderTask = ({ item }) => {
        const user = FIREBASE_AUTH.currentUser;
        const isAdminCreated = item.createdBy === "admin"; 
        const isCurrentUserAdmin = isAdminView;
        const isAdminViewUserTasks = isAdminView && viewingUserId !== user?.uid;

        return (
            <View style={styles.taskCard}>
            <View style={styles.taskHeader}>

                {/* admin viewing user tasks, task status (dot) else check-box */}
                {isAdminViewUserTasks ? (
                    <View style={styles.statusContainer}>
                        <Icon 
                            name="circle" 
                            type="font-awesome" 
                            size={14} 
                            color={item.completed ? "green" : "#FFDB58"} 
                            style={styles.statusDot}
                        />
                        <Text style={styles.statusText}>
                            {item.completed ? "Completed" : "In-progress"}
                        </Text>
                    </View>
                ) : (
                    <TouchableOpacity 
                        style={styles.checkbox}
                        onPress={() => toggleTaskCompletion(item.id, item.completed)}
                    >
                        <Icon 
                            name={item.completed ? "check-box" : "check-box-outline-blank"} 
                            size={18} 
                            color={item.completed ? "#007AFF" : "#666"}
                        />
                    </TouchableOpacity>
                )}

                <View style={styles.taskHeaderLeft}>
                    <Text style={styles.date}>
                        {format(new Date(item.date), 'dd/MM/yyyy')}
                    </Text>
                </View>

                <View style={styles.taskActions}>
                    {/* disable edit & delete if task is from Admin for users*/}
                    <TouchableOpacity 
                        onPress={() => !item.completed && (isCurrentUserAdmin || !isAdminCreated) && handleEditTask(item)}
                        disabled={item.completed || (!isCurrentUserAdmin && isAdminCreated)}
                    >
                        <Icon name="edit" size={15} color={(item.completed || (!isCurrentUserAdmin && isAdminCreated)) ? "#ccc" : "#007AFF"} />
                    </TouchableOpacity>

                    <TouchableOpacity 
                        onPress={() => (isCurrentUserAdmin || !isAdminCreated) && handleDeleteTask(item.id)}
                        disabled={!isCurrentUserAdmin && isAdminCreated}
                    >
                        <Icon name="delete" size={15} color={(!isCurrentUserAdmin && isAdminCreated) ? "#ccc" : "#FF3B30"} />
                    </TouchableOpacity>
                </View>
            </View>

            <View style={styles.taskContent}>
                <Text style={[
                    styles.heading,
                    item.completed && styles.completedText
                ]}>
                    {item.heading}
                </Text>
            </View>
            <Text style={[
                styles.description,
                item.completed && styles.completedText
            ]}>
                {item.description}
            </Text>
            <Text style={styles.deadline}>Deadline: {item.deadline}</Text>
        </View>
    );
}
    
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                {/* {showMenu && (
                    <TouchableOpacity onPress={() => navigation.openDrawer()}>
                        <Icon name="menu" size={22} color="#333" />
                    </TouchableOpacity>
                )} */}

                {/* back arrow for admin and users in tasks */}
                {(isAdminView || viewingUserId === FIREBASE_AUTH.currentUser?.uid) ? (
                    <TouchableOpacity 
                        onPress={() => {
                            if (isAdminView && viewingUserId !== FIREBASE_AUTH.currentUser?.uid) {
                                navigation.navigate("Admin"); // Admin when viewing user tasks
                            } else {
                                navigation.navigate("AdminTasks"); // AdminTasks when viewing own tasks
                            }
                        }}
                        style={styles.backButton}  
                    >
                        <Icon name="arrow-left" type="feather" size={20} color="#333" />
                    </TouchableOpacity>
                ) : showMenu && (
                    <TouchableOpacity onPress={() => navigation.openDrawer()}>
                        <Icon name="menu" size={22} color="#333" />
                    </TouchableOpacity>
                )}

                <Text style={styles.title}>
                    {isAdminView 
                        ? `${viewingUsername}'s Tasks - ${format(new Date(selectedDate), 'dd/MM/yyyy')}`
                        : `Tasks for ${format(new Date(selectedDate), 'dd/MM/yyyy')}`
                    }
                </Text>
            </View>
            {loading ? (
                <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
            ) : tasks.length === 0 ? (
                <View style={styles.noTasksContainer}>
                    <Text style={styles.noTasksText}>No tasks for this date</Text>
                </View>
            ) : (
                <FlatList
                data={tasks}
                renderItem={renderTask}
                keyExtractor={(item) => item.id}
                />
            )}

            {/* floating "+" button */}
            <TouchableOpacity style={styles.addButton} onPress={handleAddTask}>
                <Icon name="add" size={30} color="white" />
            </TouchableOpacity>
    
           {/* add task modal */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={modalAddVisible}
                onRequestClose={() => setModalAddVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>Add Task for {selectedDate}</Text>
                            <TouchableOpacity onPress={() => setModalAddVisible(false)}>
                                <Icon name="close" size={20} color="black" />
                            </TouchableOpacity>
                        </View>
    
                        <View style={styles.inputContainer}>
                            <Text style={styles.inputLabel}>Task Heading</Text>
                            <TextInput
                                style={styles.input}
                                value={editHeading}
                                onChangeText={setEditHeading}
                            />
    
                            <Text style={styles.inputLabel}>Description</Text>
                            <TextInput
                                style={[styles.input, styles.textArea]}
                                value={editDescription}
                                onChangeText={setEditDescription}
                                multiline={true}
                                numberOfLines={4}
                            />
    
                            <Text style={styles.inputLabel}>Deadline</Text>
                            <TextInput
                                style={styles.input}
                                value={editDeadline}
                                onChangeText={setEditDeadline}
                            />
                        </View>
    
                        <TouchableOpacity 
                            style={styles.saveButton} 
                            onPress={handleSaveTask}
                        >
                            <Text style={styles.saveButtonText}>Save Task</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

            {/* edit task modal */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={modalEditVisible}
                onRequestClose={() => setModalEditVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>Add Task for {selectedDate}</Text>
                            <TouchableOpacity onPress={() => setModalEditVisible(false)}>
                                <Icon name="close" size={20} color="black" />
                            </TouchableOpacity>
                        </View>
    
                        <View style={styles.inputContainer}>
                            <Text style={styles.inputLabel}>Task Heading</Text>
                            <TextInput
                                style={styles.input}
                                value={editHeading}
                                onChangeText={setEditHeading}
                            />
    
                            <Text style={styles.inputLabel}>Description</Text>
                            <TextInput
                                style={[styles.input, styles.textArea]}
                                value={editDescription}
                                onChangeText={setEditDescription}
                                multiline={true}
                                numberOfLines={4}
                            />
    
                            <Text style={styles.inputLabel}>Deadline</Text>
                            <TextInput
                                style={styles.input}
                                value={editDeadline}
                                onChangeText={setEditDeadline}
                            />
                        </View>
    
                        <TouchableOpacity 
                            style={styles.saveButton} 
                            onPress={handleUpdateTask}
                        >
                            <Text style={styles.saveButtonText}>Update Task</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        </View>
    );    
}

const styles = StyleSheet.create({
    backButton: {
        position: "absolute",
        left: 10,
        top: "50%",
        transform: [ {translateX: -10}], 
        padding: 10,
        zIndex: 10,  
    },    
    .
    .
    .
});

 For the above, I'm not able to decode that, why is viewingUserId is "null" and still I'm not able to see back arrow while admin is 
 viewing Admin Tasks. While the admin is able to retrieve its tasks with back arrow, the users viewing their tasks doesn't have menu 
 thing and clicking it gives error. So, what to do to satisfy admin and users thing, when I usually coded, either back arrows admin page /or back arrows user tasks page. 

//----------------------------------------------------------------------------------------------------------------------------------------

prompt for leave application user side 

The code, is about the react native code, it is of the users side of the time sheet app, applying with an application for leave. 
I want to add things to the code - 1) Instead of entering date as string, I want to add as two separate dates, by calender icon option, 
the start date of the leave application and end date of leave application. 2) While, I submit a leave application, it is giving firebase error. 
Like some insuffiecient permissions, in the code, I think the part is missing where the user can store the leave applications on firestore and 
also in app on users side and be able to send to admin side after selecting submit.            3) After the admin has approved, disapproved or 
in progress, the users will get to know the state of application with a dot below indicating the state of the leave application. default is grey, 
which is the admin hasn't seen the leave application yet.

//---------------------------------------------------------------------------------------------------------------------------------------------

AdminLeave.js

import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, Button, StyleSheet, TouchableOpacity, Modal, Alert} from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { collection, addDoc, serverTimestamp, getDoc, doc } from 'firebase/firestore';
import { Icon } from 'react-native-elements';
import Ionicons from '@expo/vector-icons/Ionicons';

export default function AdminLeave({ navigation }) { 
    const [leaveRequests, setLeaveRequests] = useState([]);

    useEffect(() => {
            const fetchUserData = async () => {
                const user = FIREBASE_AUTH.currentUser;
                if (user) {
                    const userDoc = await getDoc(doc(FIRESTORE_DB, 'users', user.uid));
                    if (userDoc.exists()) {
                        setUsername(userDoc.data().username);
                    } else {
                        setUsername('User');
                    }
                } else {
                    setUsername('Guest');
                }
            };
    
            fetchUserData();
        }, []);

    // Fetch leave applications from Firestore
    useEffect(() => {
        const fetchLeaves = async () => {
            try {
                const querySnapshot = await getDocs(collection(FIRESTORE_DB, 'leaveRequests'));
                setLeaveRequests(querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));
            } catch (error) {
                console.error('Error fetching leave requests:', error);
                Alert.alert('Error', 'Could not load leave applications');
            }
        };
        fetchLeaves();
    }, []);

    // Handle status update (Approve, On Hold, Disapprove)
    const handleUpdateStatus = async (id, status) => {
        try {
            await updateDoc(doc(FIRESTORE_DB, 'leaveRequests', id), { status });
            setLeaveRequests(prev =>
                prev.map(req => (req.id === id ? { ...req, status } : req))
            );
            Alert.alert('Success', `Leave request ${status}`);
        } catch (error) {
            console.error('Error updating leave request status:', error);
            Alert.alert('Error', 'Failed to update leave request status');
        }
    };

    // Handle Logout
    const handleLogout = () => {
        FIREBASE_AUTH.signOut()
            .then(() => {
                console.log('User logged out');
                navigation.navigate('Login'); 
            })
            .catch((error) => {
                console.error('Error logging out:', error);
            });
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.openDrawer()}>  {/* ✅ FIXED: openDrawer now works */}
                    <Icon name="menu" size={25} color="#333" />
                </TouchableOpacity>
                <Text style={styles.title}>Leave Applications</Text>
                <TouchableOpacity onPress={handleLogout}>
                    <Icon name="logout" size={25} color="#333" />
                </TouchableOpacity>
            </View>

            {leaveRequests.length === 0 ? (
                <Text style={styles.noRequests}>No leave applications available</Text>
            ) : (
                <FlatList
                    data={leaveRequests}
                    keyExtractor={(item) => item.id}
                    renderItem={({ item }) => (
                        <View style={styles.card}>
                            <Text style={styles.username}>User: {item.username}</Text>
                            <Text style={styles.date}>Date: {item.date}</Text>
                            <Text style={styles.reason}>Reason: {item.reason}</Text>
                            <Text style={[styles.status, getStatusStyle(item.status)]}>
                                Status: {item.status}
                            </Text>
                            <View style={styles.buttonRow}>
                                <TouchableOpacity
                                    style={styles.approveButton}
                                    onPress={() => handleUpdateStatus(item.id, 'Approved')}
                                >
                                    <Text style={styles.buttonText}>Approve</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={styles.holdButton}
                                    onPress={() => handleUpdateStatus(item.id, 'On Hold')}
                                >
                                    <Text style={styles.buttonText}>On Hold</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={styles.disapproveButton}
                                    onPress={() => handleUpdateStatus(item.id, 'Disapproved')}
                                >
                                    <Text style={styles.buttonText}>Disapprove</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    )}
                />
            )}
        </View>
    );
}

// Helper function for styling status text dynamically
const getStatusStyle = (status) => {
    switch (status) {
        case 'Approved':
            return { color: 'green', fontWeight: 'bold' };
        case 'On Hold':
            return { color: '#FFDB58', fontWeight: 'bold' };
        case 'Disapproved':
            return { color: 'red', fontWeight: 'bold' };
        default:
            return { color: 'gray' };
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginTop: 25,
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#f0f0f0',
    },
    header: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        backgroundColor: '#f8f8f8',
        marginBottom: 40,
    },
    title: {
        fontSize: 19,
        fontWeight: 'bold',
    },
    noRequests: { 
        textAlign: 'center', 
        fontSize: 16, 
        color: '#666' 
    },
    card: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 15,
        marginBottom: 15,
        elevation: 3,
    },
    username: { fontSize: 16, fontWeight: 'bold', color: '#333' },
    date: { fontSize: 14, color: '#666' },
    reason: { fontSize: 14, color: '#444', marginBottom: 8 },
    status: { fontSize: 14, marginBottom: 10 },
    buttonRow: { flexDirection: 'row', justifyContent: 'space-between' },
    approveButton: {
        backgroundColor: 'green',
        padding: 8,
        borderRadius: 5,
        flex: 1,
        alignItems: 'center',
        marginRight: 5,
    },
    holdButton: {
        backgroundColor: 'orange',
        padding: 8,
        borderRadius: 5,
        flex: 1,
        alignItems: 'center',
        marginRight: 5,
    },
    disapproveButton: {
        backgroundColor: 'red',
        padding: 8,
        borderRadius: 5,
        flex: 1,
        alignItems: 'center',
    },
    buttonText: { color: 'white', fontSize: 14, fontWeight: 'bold' },
});

(NOBRIDGE) ERROR  Warning: Text strings must be rendered within a <Text> component.
(NOBRIDGE) ERROR  Error fetching leave requests: [ReferenceError: Property 'getDocs' doesn't exist] [Component Stack]

//----------------------------------------------------------------------------------------------------------------------------

LeaveApplication.js
import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, Alert, FlatList } from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { collection, addDoc, serverTimestamp, query, where, onSnapshot,doc,getDoc} from 'firebase/firestore';
import DatePicker from 'react-native-date-picker';
import { Icon } from 'react-native-elements';
import Ionicons from '@expo/vector-icons/Ionicons';

export default function LeaveApplication({ navigation }) {
    const [leaveReason, setLeaveReason] = useState('');
    const [startDate, setStartDate] = useState(new Date());
    const [endDate, setEndDate] = useState(new Date());
    const [openStartDatePicker, setOpenStartDatePicker] = useState(false);
    const [openEndDatePicker, setOpenEndDatePicker] = useState(false);
    const [leaveApplications, setLeaveApplications] = useState([]);
    const [userRole, setUserRole] = useState('user');

    useEffect(() => {
        // Fetch current user's role
        const fetchUserRole = async () => {
            const user = FIREBASE_AUTH.currentUser;
            if (!user) return;

            try {
                const userDocRef = doc(FIRESTORE_DB, 'users', user.uid);
                const userDoc = await getDoc(userDocRef);
                if (userDoc.exists()) {
                    setUserRole(userDoc.data().role || 'user');
                }
            } catch (error) {
                console.error("Error fetching user role:", error);
            }
        };

        // Real-time listener for user's leave applications
        const user = FIREBASE_AUTH.currentUser;
        if (!user) return;

        fetchUserRole();

        const q = query(
            collection(FIRESTORE_DB, 'leaveRequests'), 
            where('userId', '==', user.uid)
        );

        const unsubscribe = onSnapshot(q, (querySnapshot) => {
            const applications = querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data(),
                canModify: doc.data().status === 'pending'
            }));
            setLeaveApplications(applications);
        }, (error) => {
            console.error("Error fetching leave applications:", error);
        });

        // Cleanup subscription on unmount
        return () => unsubscribe();
    }, []);

    const formatDate = (date) => {
        return date.toISOString().split('T')[0];
    };

    const handleSubmit = async () => {
        // Validation
        if (!leaveReason.trim()) {
            Alert.alert('Error', 'Please provide a reason for leave');
            return;
        }

        const user = FIREBASE_AUTH.currentUser;
        if (!user) {
            Alert.alert('Error', 'User not logged in');
            return;
        }

        // Calculate days of leave
        const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 3600 * 24)) + 1;

        // Prepare leave request
        const leaveRequest = {
            userId: user.uid,
            userEmail: user.email, 
            reason: leaveReason,
            startDate: formatDate(startDate),
            endDate: formatDate(endDate),
            status: 'pending',
            createdAt: serverTimestamp(),
            numberOfDays: days
        };

        try {
            // Add to leave requests collection
            await addDoc(
                collection(FIRESTORE_DB, 'leaveRequests'), 
                leaveRequest
            );

            Alert.alert('Success', 'Leave request submitted successfully');
            setLeaveReason('');
            setStartDate(new Date());
            setEndDate(new Date());
        } catch (error) {
            console.error('Error submitting leave:', error);
            Alert.alert('Error', 'Could not submit leave request. Check your permissions.');
        }
    };

    // Status color mapping
    const getStatusColor = (status) => {
        switch(status) {
            case 'approved': return 'green';
            case 'rejected': return 'red';
            case 'pending': return 'gray';
            default: return 'gray';
        }
    };

    const handleLogout = () => {
        FIREBASE_AUTH.signOut()
            .then(() => {
                    console.log('User logged out');
                    navigation.navigate('Login'); 
                })
                .catch((error) => {
                    console.error('Error logging out:', error);
                });
    };

    // Render individual leave application
    const renderLeaveApplication = ({ item }) => (
        <View style={styles.leaveApplicationCard}>
            <View style={styles.leaveApplicationHeader}>
                <Text style={styles.leaveApplicationDates}>
                    {item.startDate} to {item.endDate}
                </Text>
                <View 
                    style={[
                        styles.statusDot, 
                        { backgroundColor: getStatusColor(item.status) }
                    ]} 
                />
            </View>
            <Text style={styles.leaveApplicationReason}>
                Reason: {item.reason}
            </Text>
            <Text style={styles.leaveApplicationStatus}>
                Status: {item.status.toUpperCase()}
            </Text>
            {item.adminComment && (
                <Text style={styles.adminComment}>
                    Admin Comment: {item.adminComment}
                </Text>
            )}
        </View>
    );

    
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.openDrawer()}>
                    <Icon name="menu" size={22} color="#333" />
                </TouchableOpacity>
                <Text style={styles.title}>Apply for Leave</Text>
                <TouchableOpacity onPress={handleLogout}>
                    <Icon name="logout" size={22} color="#333" />
                </TouchableOpacity>
            </View>
            
            {userRole === 'user' && (
                <View style={styles.datePickerContainer}>
                    <View style={styles.dateContainer}>
                        <Text>Start Date:</Text>
                        <TouchableOpacity 
                            style={styles.datePickerButton} 
                            onPress={() => setOpenStartDatePicker(true)}
                        >
                            <Text>{formatDate(startDate)}</Text>
                            <Ionicons name="calendar" size={20} color="#007AFF" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.dateContainer}>
                        <Text>End Date:</Text>
                        <TouchableOpacity 
                            style={styles.datePickerButton} 
                            onPress={() => setOpenEndDatePicker(true)}
                        >
                            <Text>{formatDate(endDate)}</Text>
                            <Ionicons name="calendar" size={20} color="#007AFF" />
                        </TouchableOpacity>
                    </View>

                    <TextInput
                        style={[styles.input, styles.textArea]}
                        placeholder="Enter reason for leave"
                        value={leaveReason}
                        onChangeText={setLeaveReason}
                        multiline
                    />

                    <TouchableOpacity 
                        style={styles.submitButton} 
                        onPress={handleSubmit}
                    >
                        <Text style={styles.buttonText}>Submit Leave Request</Text>
                    </TouchableOpacity>
                </View>
            )}

            {/* DatePicker modals rendered outside the view hierarchy */}
            <View>
                <DatePicker
                    modal
                    open={openStartDatePicker}
                    date={startDate}
                    mode="date"
                    onConfirm={(date) => {
                        setOpenStartDatePicker(false);
                        setStartDate(date);
                        if (endDate < date) {
                            setEndDate(date);
                        }
                    }}
                    onCancel={() => {
                        setOpenStartDatePicker(false);
                    }}
                />
            </View>

            <View>
                <DatePicker
                    modal
                    open={openEndDatePicker}
                    date={endDate}
                    mode="date"
                    onConfirm={(date) => {
                        setOpenEndDatePicker(false);
                        setEndDate(date);
                    }}
                    onCancel={() => {
                        setOpenEndDatePicker(false);
                    }}
                />
            </View>

            <View style={styles.leaveApplicationsContainer}>
                <Text style={styles.leaveApplicationsTitle}>Previous Leave Applications</Text>
                <FlatList
                    data={leaveApplications}
                    renderItem={renderLeaveApplication}
                    keyExtractor={(item) => item.id}
                    ListEmptyComponent={
                        <Text style={styles.noApplicationsText}>
                            No leave applications found
                        </Text>
                    }
                />
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
        backgroundColor: '#f0f0f0',
        marginTop: 25,
    },
    header: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        backgroundColor: '#f8f8f8',
        marginBottom: 20,
    },
    title: {
        fontSize: 19,
        fontWeight: 'bold',
    },
    datePickerContainer: {
        marginBottom: 20,
    },
    dateContainer: {
        flexDirection: 'row', 
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 10,
        padding: 10,
        backgroundColor: 'white',
        borderRadius: 5,
    },
    input: { 
        borderWidth: 1, 
        padding: 10, 
        marginBottom: 10, 
        borderRadius: 5,
        backgroundColor: 'white',
    },
    textArea: { 
        height: 80, 
        textAlignVertical: 'top' 
    },
    submitButton: { 
        backgroundColor: '#007AFF', 
        padding: 10, 
        borderRadius: 5, 
        alignItems: 'center',
    },
    buttonText: { 
        color: 'white', 
        fontSize: 16 
    },
    leaveApplicationsContainer: {
        flex: 1,
    },
    leaveApplicationsTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 10,
    },
    leaveApplicationCard: {
        backgroundColor: 'white',
        padding: 15,
        borderRadius: 10,
        marginBottom: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    leaveApplicationHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 10,
    },
    leaveApplicationDates: {
        fontWeight: 'bold',
    },
    leaveApplicationReason: {
        marginBottom: 5,
    },
    leaveApplicationStatus: {
        fontStyle: 'italic',
    },
    adminComment: {
        marginTop: 5,
        fontStyle: 'italic',
        color: '#666',
    },
    statusDot: {
        width: 10,
        height: 10,
        borderRadius: 5,
    },
    noApplicationsText: {
        textAlign: 'center',
        color: 'gray',
        marginTop: 20,
    }
});

The above code is giving below errors:
(NOBRIDGE) ERROR  Warning: TypeError: Cannot read property 'openPicker' of null
(NOBRIDGE) ERROR  Warning: TypeError: Cannot read property 'openPicker' of null
What is this error and how to fix it?
I've been getting this error since yesterday, while I select the start date and end date, it is giving this error.





// Dashboard

import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, Button, StyleSheet, TouchableOpacity, Modal, Alert, Pressable, ScrollView,  Platform } from 'react-native';
import { Calendar } from 'react-native-calendars';
import { Picker } from '@react-native-picker/picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { collection, addDoc, serverTimestamp, getDoc, doc, setDoc, updateDoc, getDocs } from 'firebase/firestore';
import { format } from 'date-fns';
import { Icon } from 'react-native-elements';
import Ionicons from '@expo/vector-icons/Ionicons';
import { minTime } from 'date-fns/constants';

const months = [
    { label: 'January', value: 1 },
    { label: 'February', value: 2 },
    { label: 'March', value: 3 },
    { label: 'April', value: 4 },
    { label: 'May', value: 5 },
    { label: 'June', value: 6 },
    { label: 'July', value: 7 },
    { label: 'August', value: 8 },
    { label: 'September', value: 9 },
    { label: 'October', value: 10 },
    { label: 'November', value: 11 },
    { label: 'December', value: 12 },
];

export default function Dashboard({ navigation }) {
    const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
    const [selectedDate, setSelectedDate] = useState('');
    const [modalVisible, setModalVisible] = useState(false);
    const [taskHeading, setTaskHeading] = useState('');
    const [taskDescription, setTaskDescription] = useState('');
    // const [taskDeadline, setTaskDeadline] = useState('');
    const [deadlineDate, setDeadlineDate] = useState(new Date());
    const [showPicker, setShowPicker] = useState(false);
    const [pickerMode, setPickerMode] = useState('date');
    const [username, setUsername] = useState('');
    const [checkInTime, setCheckInTime] = useState(null);
    const [checkOutTime, setCheckOutTime] = useState(null);
    const [workedHours, setWorkedHours] = useState(null);
    const [totalWorkedMinutes, setTotalWorkedMinutes] = useState(0);
    const [attendanceId, setAttendanceId] = useState(null);
    const [isCheckInDisabled, setIsCheckInDisabled] = useState(false);
    const [isCheckOutDisabled, setIsCheckOutDisabled] = useState(true);
    const [leaveData, setLeaveData] = useState({});

    const user = FIREBASE_AUTH.currentUser;

    useEffect(() => {
        const fetchUserData = async () => {
            const user = FIREBASE_AUTH.currentUser;
            if (user) {
                const userDoc = await getDoc(doc(FIRESTORE_DB, 'users', user.uid));
                if (userDoc.exists()) {
                    setUsername(userDoc.data().username);
                } else {
                    setUsername('User');
                }
            } else {
                setUsername('Guest');
            }
        };

        fetchUserData();
    }, []);

    useEffect(() => {
        fetchAttendance();
    }, []);

    // Fetch attendance data
    // const fetchAttendance = async () => {
    //     if (!user) return;

    //     try {
    //         const docRef = doc(FIRESTORE_DB, 'attendance', user.uid);
    //         const docSnap = await getDoc(docRef);

    //         if (docSnap.exists()) {
    //             const data = docSnap.data();
    //             setCheckInTime(data.checkInTime);
    //             setCheckOutTime(data.checkOutTime);
    //             setWorkedHours(data.workedHours);

    //             // Disable/Enable Buttons Based on Data
    //             if (data.checkInTime && !data.checkOutTime) {
    //                 setIsCheckInDisabled(true);
    //                 setIsCheckOutDisabled(false);
    //             } else {
    //                 setIsCheckInDisabled(false);
    //                 setIsCheckOutDisabled(true);
    //             }
    //         } else {
    //             console.log("No attendance record found, creating new one.");
    //             setIsCheckInDisabled(false);
    //             setIsCheckOutDisabled(true);
    //         }
    //     } catch (error) {
    //         console.error("Error fetching attendance:", error);
    //     }
    // };

    const fetchAttendance = async () => {
        if (!user) return;

        try {
            const docRef = doc(FIRESTORE_DB, 'attendance', user.uid);
            const docSnap = await getDoc(docRef);

            const todayDate = new Date().toLocaleDateString();

            if (docSnap.exists()) {
                const data = docSnap.data();

                // If it's a new day, reset everything
                if (data.date !== todayDate) {
                    await updateDoc(docRef, {
                        checkInTime: null,
                        checkOutTime: null,
                        workedHours: '',
                        totalWorkedMinutes: 0,
                        date: todayDate,
                        timestamp: serverTimestamp(),
                    });
                    setCheckInTime(null);
                    setCheckOutTime(null);
                    setWorkedHours('');
                    setTotalWorkedMinutes(0);
                    setIsCheckInDisabled(false);
                    setIsCheckOutDisabled(true);
                } else {
                    setCheckInTime(data.checkInTime);
                    setCheckOutTime(data.checkOutTime);
                    setWorkedHours(data.workedHours);
                    setTotalWorkedMinutes(data.totalWorkedMinutes || 0);

                    // Disable/Enable Buttons
                    if (data.checkInTime) {
                        setIsCheckInDisabled(true);
                        // Always enable check-out if checked in
                        setIsCheckOutDisabled(false);
                    } else {
                        setIsCheckInDisabled(false);
                        setIsCheckOutDisabled(true);
                    }
                }
            } else {
                console.log("No attendance record found, creating new one.");
                setIsCheckInDisabled(false);
                setIsCheckOutDisabled(true);
            }
        } catch (error) {
            console.error("Error fetching attendance:", error);
        }
    };

    const handleMonthChange = (itemValue) => {
        setSelectedMonth(itemValue);
    };

    const handleYearChange = (itemValue) => {
        setSelectedYear(itemValue);
    };

    const getCurrentMonthDays = () => {
        const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();
        const markedDates = {

        };
        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(selectedYear, selectedMonth - 1, day).toISOString().split('T')[0];
            markedDates[date] = { marked: false }; // have to customize more
        }
        return markedDates;
    };

    const handleDayPress = (day) => {
        setSelectedDate(day.dateString);
        setModalVisible(true);
    };

    const handleSaveTask = async () => {
        try {
            // Form validation
            if (!taskHeading.trim()) {
                Alert.alert('Error', 'Please enter a task heading');
                return;
            }
            if (!taskDescription.trim()) {
                Alert.alert('Error', 'Please enter a task description');
                return;
            }

            const user = FIREBASE_AUTH.currentUser;
            if (!user) {
                Alert.alert('Error', 'No user logged in');
                return;
            }

            // Format the deadline date
            const formattedDeadline = format(deadlineDate, 'dd/MM/yyyy - hh:mm a');

            const newTask = {
                userId: user.uid,
                email: user.email,
                username: username,
                date: new Date(selectedDate).toISOString(),
                heading: taskHeading.trim(),
                description: taskDescription.trim(),
                deadline: formattedDeadline,
                completed: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Add task to Firestore
            const docRef = await addDoc(collection(FIRESTORE_DB, 'tasks'), newTask);

            // Show success message
            Alert.alert('Success', 'Task added successfully');

            // Navigate to Tasks screen with both date and new task
            navigation.navigate('Tasks', {
                date: selectedDate,
                newTask: { id: docRef.id, ...newTask }
            });

            // Reset form and close modal
            setTaskHeading('');
            setTaskDescription('');
            setDeadlineDate(new Date());
            setModalVisible(false);
        } catch (error) {
            console.error('Error saving task:', error);
            Alert.alert('Error', 'Failed to save task. Please try again.');
        }
    };

    const viewTasksForDate = (date) => {
        navigation.navigate('Tasks', {
            date: date,
            timestamp: new Date().getTime()
        });
    };

    // Check-In
    const handleCheckIn = async () => {
        if (!user) return;

        try {
            const checkInTimestamp = new Date().toISOString();
            setCheckInTime(checkInTimestamp);
            setCheckOutTime(null);
            setIsCheckInDisabled(true);
            setIsCheckOutDisabled(false);

            const docRef = doc(FIRESTORE_DB, 'attendance', user.uid);
            await setDoc(docRef, {
                checkInTime: checkInTimestamp,
                checkOutTime: null,
                workedHours: 0,
                totalWorkedMinutes: totalWorkedMinutes, // Preserve existing total
                userId: user.uid,
                email: user.email,
                username: username,
                date: new Date().toLocaleDateString(),
                timestamp: serverTimestamp(),
            });

            console.log("Check-In saved in Firestore:", checkInTimestamp);
            Alert.alert("Success", `Checked In at ${formatDateTime(checkInTimestamp)}!`);
        } catch (error) {
            console.error("Error during check-in:", error);
            Alert.alert("Error", "Failed to Check In");
        }
    };

    // Check-Out
    const handleCheckOut = async () => {
        if (!user || !checkInTime) return;

        try {
            const checkOutTimestamp = new Date().toISOString();

            // Calculate time worked for this session
            const diffMs = new Date(checkOutTimestamp) - new Date(checkInTime);
            const session = Math.floor(diffMs / (1000 * 60));

            // Add to total worked minutes
            const newTotalMinutes = session;
            setTotalWorkedMinutes(newTotalMinutes);

            // Calculate total hours and minutes for display
            const totalHours = Math.floor(newTotalMinutes / 60);
            const remainingMinutes = newTotalMinutes % 60;
            const totalWorkedDuration = `${totalHours} hrs ${remainingMinutes} mins`;

            // Calculate session duration for display
            const sessionHours = Math.floor(session / 60);
            const sessionRemainingMinutes = session % 60;
            const sessionDuration = `${sessionHours} hrs ${sessionRemainingMinutes} mins`;

            const attendanceRef = doc(FIRESTORE_DB, 'attendance', user.uid);
            await updateDoc(attendanceRef, {
                checkOutTime: checkOutTimestamp,
                workedHours: totalWorkedDuration,
                totalWorkedMinutes: newTotalMinutes
            });

            setCheckOutTime(checkOutTimestamp);
            setWorkedHours(totalWorkedDuration);

            // Don't disable check-out button after checking out
            // This allows multiple check-outs

            Alert.alert(
                "Success",
                `Checked Out - ${formatDateTime(checkOutTimestamp)}!\n\nThis session: ${sessionDuration}\nWorked Hours: ${totalWorkedDuration}`
            );

        } catch (error) {
            console.error("Error during check-out:", error);
            Alert.alert("Error", "Failed to Check Out");
        }
    };

    // function to format date strings
    const formatDateTime = (dateTimeString) => {
        if (!dateTimeString) return 'Not Available';

        try {
            const date = new Date(dateTimeString);
            return format(date, 'dd-MM-yyyy HH:mm');
        } catch (error) {
            console.error('Error formatting date:', error);
            return dateTimeString;
        }
    };

    const fetchLeaveRequest = async () => {
        if(!user) return;

        try {
            const leaveQuery = query(
                collection(FIRESTORE_DB, 'leaveRequests'),
                where('userId', '==', user.uid),
                where('status', '==', 'approved')
            );

            const querySnapshot = await getDocs(leaveQuery);
            const leaveData = {};

            // processing each leave request
            querySnapshot.forEach(doc => {
                const leave = doc.data();
                const startDate = new Date(leave.startDate);
                const endDate = new Date(leave.endDate);

                // Mark all days in the leave period
                const currentDate = new Date(startDate);
                while (currentDate <= endDate) {
                    const dateStr = currentDate.toISOString().split('T')[0];
                    leaveData[dateStr] = {
                        leaveType: leave.leaveType,
                        status: 'leave'
                    };

                    // Move to next day
                    currentDate.setDate(currentDate.getDate() + 1);
                }
            });

        return leaveData;
    } catch (error) {
        console.error('Error fetching leave requests:', error);
        return {};
        }
    }

    const handleLogout = () => {
        FIREBASE_AUTH.signOut()
            .then(() => {
                console.log('User logged out');
                navigation.navigate('Login');
            })
            .catch((error) => {
                console.error('Error logging out:', error);
            });
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.openDrawer()}>
                    <Icon name="menu" size={25} color="#333" />
                </TouchableOpacity>
                <Text style={styles.title}>Dashboard</Text>
                <TouchableOpacity onPress={handleLogout}>
                    <Icon name="logout" size={25} color="#333" />
                </TouchableOpacity>
            </View>

            <View>
                <Text style={[styles.usertext, { textAlign: 'left' }]}>Hello, {username}!</Text>
            </View>

            {/* check in & check out feature */}
            <View style={styles.attendanceCard}>
                <Text style={styles.checkinText}>Check-In Time: {checkInTime ? formatDateTime(checkInTime) : 'Not Checked In'}</Text>
                <Text style={styles.checkinText}>Check-Out: {checkOutTime ? formatDateTime(checkOutTime) : 'Not Checked Out'}</Text>
                <Text style={styles.checkinText}>Worked Hours: {workedHours ? `${workedHours}` : 'N/A'}</Text>
            </View>

            <View style={styles.buttonRow}>
                <Pressable
                    style={[styles.checkinButton, isCheckInDisabled && styles.disabledButton]}
                    onPress={handleCheckIn}
                    disabled={isCheckInDisabled}
                >
                    <Text style={styles.buttonText}>Check In</Text>
                </Pressable>

                <Pressable
                    style={[styles.checkinButton, isCheckOutDisabled && styles.disabledButton]}
                    onPress={handleCheckOut}
                    disabled={isCheckOutDisabled}
                >
                    <Text style={styles.buttonText}>Check Out</Text>
                </Pressable>
            </View>

            {/* month and year picker */}
            <View style={styles.pickerContainer}>
                <Picker
                    selectedValue={selectedMonth}
                    style={styles.picker}
                    onValueChange={handleMonthChange}
                >
                    {months.map((month) => (
                        <Picker.Item key={month.value} label={month.label} value={month.value} />
                    ))}
                </Picker>
                <Picker
                    selectedValue={selectedYear}
                    style={styles.picker}
                    onValueChange={handleYearChange}
                >
                    {[...Array(20).keys()].map(year => (
                        <Picker.Item key={year} label={`${selectedYear - 10 + year}`} value={selectedYear - 10 + year} />
                    ))}
                </Picker>
            </View>

            {/* calendar */}
            <View style={styles.card}>
                <Calendar
                    key={`${selectedYear}-${selectedMonth}`}
                    style={styles.calendar}
                    hideArrows={true}
                    theme={{
                        calendarBackground: 'white',
                        textSectionTitleColor: '#b6c1cd',
                        selectedDayBackgroundColor: '#00adf5',
                        selectedDayTextColor: '#ffffff',
                        todayTextColor: '#00adf5',
                        dayTextColor: '#2d4150',
                        textDisabledColor: '#d9e1e8',
                        dotColor: '#00adf5',
                        selectedDotColor: '#ffffff',
                        arrowColor: 'orange',
                        monthTextColor: 'black',
                        indicatorColor: 'black',
                        textDayFontWeight: '300',
                        textMonthFontWeight: 'bold',
                        textDayHeaderFontWeight: '300',
                        textDayFontSize: 16,
                        textMonthFontSize: 16,
                        textDayHeaderFontSize: 14,
                    }}
                    markedDates={getCurrentMonthDays()}
                    current={`${selectedYear}-${selectedMonth.toString().padStart(2, '0')}-01`}
                    onDayPress={(day) => {
                        setSelectedDate(day.dateString);
                        Alert.alert(
                            'Select Option',
                            `What would you like to do for ${day.dateString}?`,
                            [
                                {
                                    text: 'Add Task',
                                    onPress: () => setModalVisible(true)
                                },
                                {
                                    text: 'View Tasks',
                                    onPress: () => viewTasksForDate(day.dateString)
                                },
                                {
                                    text: 'Cancel',
                                    style: 'cancel'
                                }
                            ]
                        );
                    }}
                />
            </View>

            <Modal
                animationType="slide"
                presentationStyle='pageSheet'
                visible={modalVisible}
                onRequestClose={() => {
                    setModalVisible(false);
                    setShowPicker(false);
                }}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}> Add Task for {selectedDate}</Text>
                            <TouchableOpacity
                                onPress={() => {
                                    setModalVisible(false);
                                    setShowPicker(false);
                                }}
                                style={styles.closeButton}
                            >
                                <Ionicons name="close" size={24} color="black" />
                            </TouchableOpacity>
                        </View>

                        <View style={styles.inputContainer}>
                            <Text style={styles.inputLabel}>Task Heading</Text>
                            <TextInput
                                style={styles.input}
                                placeholder="Enter task heading"
                                value={taskHeading}
                                onChangeText={setTaskHeading}
                            />

                            <Text style={styles.inputLabel}>Description</Text>
                            <TextInput
                                style={[styles.input, styles.textArea]}
                                placeholder="Enter task description"
                                value={taskDescription}
                                onChangeText={setTaskDescription}
                                multiline={true}
                                numberOfLines={4}
                            />

                            <Text style={styles.inputLabel}>Deadline</Text>
                            <View style={styles.dateTimeButtonsContainer}>
                                <TouchableOpacity
                                    style={styles.dateTimeButton}
                                    onPress={() => {
                                        // Ensure any existing picker is closed first
                                        setShowPicker(false);
                                        // Use setTimeout to ensure the previous picker is fully closed
                                        setTimeout(() => {
                                            setPickerMode('date');
                                            setShowPicker(true);
                                        }, 100);
                                    }}
                                >
                                    <Icon name="calendar" type="feather" size={16} color="#007AFF" />
                                    <Text style={styles.dateTimeButtonText}>Select Date</Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.dateTimeButton}
                                    onPress={() => {
                                        // Ensure any existing picker is closed first
                                        setShowPicker(false);
                                        // Use setTimeout to ensure the previous picker is fully closed
                                        setTimeout(() => {
                                            setPickerMode('time');
                                            setShowPicker(true);
                                        }, 100);
                                    }}
                                >
                                    <Icon name="clock" type="feather" size={16} color="#007AFF" />
                                    <Text style={styles.dateTimeButtonText}>Select Time</Text>
                                </TouchableOpacity>
                            </View>

                            <View style={styles.selectedDeadlineContainer}>
                                <Text style={styles.selectedDeadlineText}>
                                    {format(deadlineDate, 'dd/MM/yyyy - hh:mm a')}
                                </Text>
                            </View>

                            {showPicker && (
                                <DateTimePicker
                                    testID="dateTimePicker"
                                    value={deadlineDate}
                                    mode={pickerMode}
                                    is24Hour={false}
                                    display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                                    onChange={(_, selectedDate) => {
                                        // Always hide the picker on Android after selection
                                        if (Platform.OS === 'android') {
                                            setShowPicker(false);
                                        }

                                        // Update the date if a selection was made
                                        if (selectedDate) {
                                            // For iOS, if in date mode, switch to time mode after date selection
                                            if (Platform.OS === 'ios' && pickerMode === 'date') {
                                                setPickerMode('time');
                                                // Create a new date with the selected date but keep current time
                                                const updatedDate = new Date(selectedDate);
                                                updatedDate.setHours(deadlineDate.getHours());
                                                updatedDate.setMinutes(deadlineDate.getMinutes());
                                                setDeadlineDate(updatedDate);
                                            }
                                            // For iOS, if in time mode, hide picker after time selection
                                            else if (Platform.OS === 'ios' && pickerMode === 'time') {
                                                setShowPicker(false);
                                                // Create a new date with the current date but selected time
                                                const updatedDate = new Date(deadlineDate);
                                                updatedDate.setHours(selectedDate.getHours());
                                                updatedDate.setMinutes(selectedDate.getMinutes());
                                                setDeadlineDate(updatedDate);
                                            }
                                            // For Android, update the date directly
                                            else {
                                                setDeadlineDate(selectedDate);
                                            }
                                        }
                                    }}
                                />
                            )}
                        </View>

                        <TouchableOpacity
                            style={styles.saveButton}
                            onPress={handleSaveTask}
                        >
                            <Text style={styles.saveButtonText}>Save Task</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginTop: 25,
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#f0f0f0',
    },
    header: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        borderRadius: 15,
        backgroundColor: '#f8f8f8',
        marginBottom: 20,
    },
    title: {
        fontSize: 19,
        fontWeight: 'bold',
    },
    pickerContainer: {
        // marginTop: -10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
    },

    picker: {
        height: 55,
        width: 160,
    },

    card: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        width: '90%',
    },

    calendar: {
        borderRadius: 10,
    },

    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },

    modalContainer: {
        width: '90%',
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },

    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
    },

    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },

    closeButton: {
        padding: 5,
    },

    inputContainer: {
        marginBottom: 20,
    },

    inputLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: '#666',
        marginBottom: 5,
    },

    input: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        marginBottom: 15,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
    },

    textArea: {
        height: 100,
        textAlignVertical: 'top',
    },

    saveButton: {
        backgroundColor: '#007AFF',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
    },

    saveButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
    usertext: {
        marginTop: -10,
        textAlign: 'justify',
        fontStyle: 'italic',
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
        marginBottom: 10,
    },
    attendanceCard: {
        // marginTop: 15,
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 10,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        width: '90%',
        marginBottom: 10,
    },
    checkinText: {
        fontSize: 15,
        fontWeight: 'bold',
        alignSelf: 'baseline',
        marginLeft: 10,
        marginBottom: 5,
    },
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
    },
    checkinButton: {
        backgroundColor: '#007AFF',
        padding: 10,
        borderRadius: 30,
        width: '40%',
        alignItems: 'center',
    },
    disabledButton: {
        backgroundColor: 'gray',
    },
    buttonText: {
        color: 'white',
        fontSize: 15,
        fontWeight: 'bold',
    },
    activeSessionText: {
        color: '#007AFF',
        fontWeight: 'bold',
        fontStyle: 'italic',
        marginTop: 5,
    },
    datePickerButton: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        marginBottom: 15,
        backgroundColor: '#f9f9f9',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    dateTimeButtonsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 10,
    },
    dateTimeButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f0f0f0',
        borderWidth: 1,
        borderColor: '#007AFF',
        borderRadius: 8,
        paddingVertical: 10,
        paddingHorizontal: 15,
        width: '48%',
    },
    dateTimeButtonText: {
        color: '#007AFF',
        fontSize: 14,
        fontWeight: '600',
        marginLeft: 5,
    },
    selectedDeadlineContainer: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        marginBottom: 15,
        backgroundColor: '#f9f9f9',
    },
    selectedDeadlineText: {
        fontSize: 16,
        color: '#333',
        textAlign: 'center',
    },
});



//-------------------------------------------------------------------------------------------------------------

package.json

    "dependencies": {
  "@expo/vector-icons": "^14.1.0",
  "@react-native-async-storage/async-storage": "2.1.2",
  "@react-native-community/datetimepicker": "8.3.0",
  "@react-native-community/masked-view": "^0.1.11",
  "@react-native-google-signin/google-signin": "^13.2.0",
  "@react-native-picker/picker": "2.11.0",
  "@react-navigation/bottom-tabs": "^7.2.0",
  "@react-navigation/drawer": "^7.1.2",
  "@react-navigation/native": "^7.0.15",
  "@react-navigation/stack": "^7.1.2",
  "date-fns": "^4.1.0",
  "expo": "53.0.0",
  "expo-blur": "~14.1.4",
  "expo-constants": "~17.1.5",
  "expo-dev-client": "~5.1.8",
  "expo-file-system": "~18.1.9",
  "expo-font": "~13.3.1",
  "expo-haptics": "~14.1.4",
  "expo-linear-gradient": "~14.1.4",
  "expo-linking": "~7.1.4",
  "expo-location": "~18.1.4",
  "expo-router": "~5.0.5",
  "expo-sharing": "~13.1.5",
  "expo-splash-screen": "~0.30.8",
  "expo-status-bar": "~2.2.3",
  "expo-symbols": "~0.4.4",
  "expo-system-ui": "~5.0.7",
  "expo-web-browser": "~14.1.6",
  "file-saver": "^2.0.5",
  "firebase": "^10.14.1",
  "idb": "7.0.1",
  "jest-expo": "~53.0.4",
  "metro": "^0.82.2",
  "metro-cache": "^0.82.2",
  "metro-config": "^0.82.2",
  "moment": "^2.30.1",
  "react": "^19.0.0",
  "react-dom": "^19.0.0",
  "react-native": "0.79.2",
  "react-native-calendar-range-picker": "^1.6.0",
  "react-native-calendars": "^1.1309.1",
  "react-native-date-picker": "^5.0.11",
  "react-native-dotenv": "^3.4.11",
  "react-native-elements": "^3.4.3",
  "react-native-fs": "^2.20.0",
  "react-native-gesture-handler": "~2.24.0",
  "react-native-html-to-pdf": "^0.12.0",
  "react-native-reanimated": "~3.17.4",
  "react-native-safe-area-context": "5.4.0",
  "react-native-screens": "~4.10.0",
  "react-native-share": "^12.0.9",
  "react-native-vector-icons": "^10.2.0",
  "react-native-web": "^0.20.0",
  "react-native-webview": "13.13.5",
  "rimraf": "^6.0.1",
  "xlsx": "^0.18.5",
  "expo-print": "~14.1.4"
},
"devDependencies": {
  "@babel/core": "^7.25.2",
  "@babel/plugin-transform-class-properties": "^7.25.9",
  "@babel/plugin-transform-private-methods": "^7.25.9",
  "@babel/plugin-transform-private-property-in-object": "^7.25.9",
  "@react-native-community/cli": "latest",
  "@react-native/babel-preset": "^0.78.2",
  "@react-native/metro-config": "^0.79.2",
  "@types/jest": "^29.5.12",
  "@types/react": "~19.0.10",
  "@types/react-test-renderer": "^18.3.0",
  "babel-preset-expo": "~13.0.0",
  "jest": "^29.2.1",
  "jest-expo": "~53.0.4",
  "metro-react-native-babel-preset": "^0.77.0",
  "react-test-renderer": "18.3.1",
  "typescript": "~5.8.3"
}

//--------------------------------------------------------------------------------------

new package.json full

{
  "name": "timesheet",
  "main": "index.js",
  "version": "1.0.0",
  "scripts": {
    "start": "expo start",
    "start-clear": "npx expo start --clear",
    "reset-project": "node ./scripts/reset-project.js",
    "fix-deps": "node ./scripts/fix-dependencies.js",
    "fix-firebase": "node ./scripts/fix-firebase-deps.js",
    "fix-idb": "node ./scripts/fix-idb.js",
    "fix-babel": "node ./scripts/fix-babel.js",
    "clear-cache": "npm install rimraf && node ./scripts/clear-metro-cache.js",
    "fix-metro": "node ./scripts/fix-metro-error.js",
    "bundle-app": "node ./scripts/bundle-app.js",
    "android": "npx expo start --android",
    "ios": "npx expo start --ios",
    "web": "npx expo start --web",
    "test": "jest --watchAll",
    "lint": "npx expo lint",
    "doctor": "npx expo doctor",
    "prebuild": "npx expo prebuild --clean",
    "start-safe": "npx expo start --no-dev --minify",
    "start-metro-fix": "npx expo start --clear --no-dev",
    "use-minimal": "node ./scripts/switch-config.js minimal",
    "use-full": "node ./scripts/switch-config.js full",
    "fix-all": "npm run fix-deps && npm run fix-idb && npm run fix-babel && npm run clear-cache && npm run start-metro-fix"
  },
  "jest": {
    "preset": "jest-expo"
  },
  "dependencies": {
  "@expo/vector-icons": "^14.0.5",
  "@react-native-async-storage/async-storage": "1.24.0",
  "@react-native-community/datetimepicker": "8.2.0",
  "@react-native-community/masked-view": "^0.1.11",
  "@react-native-google-signin/google-signin": "^12.0.2",
  "@react-native-picker/picker": "2.8.1",
  "@react-navigation/bottom-tabs": "^6.6.1",
  "@react-navigation/drawer": "^6.7.2",
  "@react-navigation/native": "^6.1.18",
  "@react-navigation/stack": "^6.4.1",
  "date-fns": "^4.1.0",
  "expo": "^53.0.0",
  "expo-blur": "~14.0.0",
  "expo-constants": "~17.0.0",
  "expo-dev-client": "~4.0.0",
  "expo-file-system": "~18.0.0",
  "expo-font": "~13.0.0",
  "expo-haptics": "~14.0.0",
  "expo-linear-gradient": "~14.0.0",
  "expo-linking": "~7.0.0",
  "expo-location": "~18.0.0",
  "expo-router": "~4.0.0",
  "expo-sharing": "~13.0.0",
  "expo-splash-screen": "~0.30.0",
  "expo-status-bar": "~2.0.0",
  "expo-symbols": "~0.4.0",
  "expo-system-ui": "~4.0.0",
  "expo-web-browser": "~14.0.0",
  "expo-print": "~14.0.0",
  "firebase": "10.12.2",
  "idb": "^7.0.1",
  "moment": "^2.30.1",
  "react": "18.3.1",
  "react-dom": "18.3.1",
  "react-native": "0.76.3",
  "react-native-calendar-range-picker": "^1.6.0",
  "react-native-calendars": "^1.1309.0",
  "react-native-date-picker": "^5.0.7",
  "react-native-dotenv": "^3.4.11",
  "react-native-elements": "^3.4.3",
  "react-native-fs": "^2.20.0",
  "react-native-gesture-handler": "~2.20.0",
  "react-native-html-to-pdf": "^0.12.0",
  "react-native-reanimated": "~3.15.0",
  "react-native-safe-area-context": "4.10.5",
  "react-native-screens": "~3.34.0",
  "react-native-share": "^11.0.3",
  "react-native-vector-icons": "^10.1.0",
  "react-native-web": "~0.19.12",
  "react-native-webview": "13.12.1",
  "rimraf": "^6.0.1",
  "xlsx": "^0.18.5"
},
"devDependencies": {
  "@babel/core": "^7.24.7",
  "@babel/plugin-transform-class-properties": "^7.24.7",
  "@babel/plugin-transform-private-methods": "^7.24.7",
  "@babel/plugin-transform-private-property-in-object": "^7.24.7",
  "@react-native-community/cli": "^15.0.0",
  "@react-native/babel-preset": "^0.76.0",
  "@react-native/metro-config": "^0.76.0",
  "@types/jest": "^29.5.12",
  "@types/react": "~18.3.3",
  "@types/react-test-renderer": "^18.3.0",
  "babel-preset-expo": "~11.0.0",
  "jest": "^29.7.0",
  "jest-expo": "~53.0.0",
  "metro-react-native-babel-preset": "^0.76.0",
  "react-test-renderer": "18.3.1",
  "typescript": "~5.5.4"
},
  "private": true,
  "codegenConfig": {
    "react-native-codegen": {
      "codegenModuleName": "MyAppCodegen",
      "libraryName": "MyApp",
      "libraryType": "Shared"
    }
  }
}















//-------------------------------------------------------------------------------------------------------------------

Attendance.js   (old with xlsx)

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ScrollView, ActivityIndicator, Modal, Share, Platform } from 'react-native';
import { FIREBASE_AUTH, FIRESTORE_DB } from '../firebaseConfig';
import { getDoc, doc, setDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addWeeks, subWeeks } from 'date-fns';
import XLSX from 'xlsx';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Location from 'expo-location';
import { Icon } from 'react-native-elements';

export default function Attendance({ navigation }) {
    const [username, setUsername] = useState('');
    const [checkInTime, setCheckInTime] = useState(null);
    const [checkOutTime, setCheckOutTime] = useState(null);
    const [workedHours, setWorkedHours] = useState(null);
    const [totalWorkedMinutes, setTotalWorkedMinutes] = useState(0);
    const [isCheckInDisabled, setIsCheckInDisabled] = useState(false);
    const [isCheckOutDisabled, setIsCheckOutDisabled] = useState(true);

    // Location states
    const [checkInLocation, setCheckInLocation] = useState(null);
    const [checkOutLocation, setCheckOutLocation] = useState(null);
    const [locationPermission, setLocationPermission] = useState(false);

    // Week and month selection for attendance records
    const currentDate = new Date();
    const [selectedWeek, setSelectedWeek] = useState(currentDate);
    const [selectedMonth, setSelectedMonth] = useState(currentDate);
    const [weeklyAttendance, setWeeklyAttendance] = useState([]);
    const [monthlyAttendance, setMonthlyAttendance] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [leaveDays, setLeaveDays] = useState({});

    // View type and modal state
    const [viewType, setViewType] = useState('weekly');
    const [modalVisible, setModalVisible] = useState(false);

    // Pagination for monthly view
    const [recordsPerPage] = useState(7);
    const [currentPage, setCurrentPage] = useState(1);

    const user = FIREBASE_AUTH.currentUser;

    useEffect(() => {
        const fetchUserData = async () => {
            const user = FIREBASE_AUTH.currentUser;
            if (user) {
                const userDoc = await getDoc(doc(FIRESTORE_DB, 'users', user.uid));
                if (userDoc.exists()) {
                    setUsername(userDoc.data().username);
                } else {
                    setUsername('User');
                }
            } else {
                setUsername('Guest');
            }
        };

        fetchUserData();
    }, []);

    useEffect(() => {
        fetchAttendance();
        requestLocationPermission();
    }, []);

    // Request location permission with enhanced error handling for SDK 53
    const requestLocationPermission = async () => {
        try {
            // First check if location services are enabled
            const isLocationServicesEnabled = await Location.hasServicesEnabledAsync();

            if (!isLocationServicesEnabled) {
                console.log('Location services are disabled');
                Alert.alert(
                    "Location Services Disabled",
                    "Please enable location services in your device settings to use location features.",
                    [{ text: "OK" }]
                );
                return;
            }

            // Check foreground permissions
            const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();

            if (foregroundStatus === 'granted') {
                console.log('Location permission granted');
                setLocationPermission(true);
            } else {
                console.log('Location permission denied');
                Alert.alert(
                    "Permission Required",
                    "Location permission is required to track check-in and check-out locations.",
                    [{ text: "OK" }]
                );
            }
        } catch (error) {
            console.error('Error requesting location permission:', error);
            Alert.alert(
                "Permission Error",
                "There was an error requesting location permissions. Please try again or check app permissions in settings.",
                [{ text: "OK" }]
            );
        }
    };

    // Get current location with enhanced error handling and timeout for SDK 53
    const getCurrentLocation = async () => {
        if (!locationPermission) {
            console.log('Location permission not granted, requesting permission');
            await requestLocationPermission();

            // If still not granted after request, return null
            if (!locationPermission) {
                return null;
            }
        }

        try {
            // Set a timeout for location request
            const locationPromise = Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.High,
                timeInterval: 5000, // Update at most every 5 seconds
                distanceInterval: 10, // Update if moved by 10 meters
            });

            // Create a timeout promise
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Location request timed out')), 15000)
            );

            // Race the location request against the timeout
            const location = await Promise.race([locationPromise, timeoutPromise]);

            if (!location) {
                throw new Error('Failed to get location');
            }

            // Get address from coordinates with error handling
            let addressResponse;
            try {
                addressResponse = await Location.reverseGeocodeAsync({
                    latitude: location.coords.latitude,
                    longitude: location.coords.longitude
                });
            } catch (geocodeError) {
                console.error('Error getting address from coordinates:', geocodeError);
                // Continue with unknown location if geocoding fails
            }

            // Format the address
            let formattedAddress = 'Unknown location';
            if (addressResponse && addressResponse.length > 0) {
                const address = addressResponse[0];
                const addressParts = [];

                if (address.name) addressParts.push(address.name);
                if (address.street) addressParts.push(address.street);
                if (address.city) addressParts.push(address.city);
                if (address.region) addressParts.push(address.region);

                formattedAddress = addressParts.join(', ');
            }

            return {
                coords: {
                    latitude: location.coords.latitude,
                    longitude: location.coords.longitude
                },
                address: formattedAddress,
                timestamp: new Date()
            };
        } catch (error) {
            console.error('Error getting current location:', error);

            // Show a user-friendly error message
            if (error.message === 'Location request timed out') {
                Alert.alert(
                    "Location Timeout",
                    "Unable to get your current location. Please check if location services are enabled and try again.",
                    [{ text: "OK" }]
                );
            } else {
                Alert.alert(
                    "Location Error",
                    "There was a problem getting your location. Please try again later.",
                    [{ text: "OK" }]
                );
            }

            return null;
        }
    };

    // Fetch attendance records when selected week/month changes
    useEffect(() => {
        console.log('Selected week changed:', selectedWeek);
        fetchLeaveData();
        fetchWeeklyAttendance();
    }, [selectedWeek]);

    // Fetch monthly attendance when selected month changes
    useEffect(() => {
        if (viewType === 'monthly') {
            console.log('Selected month changed:', selectedMonth);
            fetchMonthlyAttendance();
        }
    }, [selectedMonth, viewType]);

    // Ensure leave days are properly maintained when changing pages in monthly view
    useEffect(() => {
        if (viewType === 'monthly') {
            // Get the year and month from selectedMonth
            const year = selectedMonth.getFullYear();
            const month = selectedMonth.getMonth();

            // Create the first and last day of the month
            const firstDayOfMonth = new Date(year, month, 1);
            const lastDayOfMonth = new Date(year, month + 1, 0);

            // Refresh leave data when changing pages
            fetchMonthLeaveData(firstDayOfMonth, lastDayOfMonth);
        }
    }, [currentPage, viewType]);

    // Function to fetch approved leave requests
    const fetchLeaveData = async () => {
        if (!user) return;

        try {
            // Create date range for the selected week
            const weekDate = new Date(selectedWeek);
            const startOfWeekDate = startOfWeek(weekDate, { weekStartsOn: 0 });
            const endOfWeekDate = endOfWeek(weekDate, { weekStartsOn: 0 });

            console.log('Fetching leave data for week:', format(startOfWeekDate, 'yyyy-MM-dd'), 'to', format(endOfWeekDate, 'yyyy-MM-dd'));

            // Query approved leave requests for the current user
            const leaveQuery = query(
                collection(FIRESTORE_DB, 'leaveRequests'),
                where('userId', '==', user.uid),
                where('status', '==', 'Approved')
            );

            const querySnapshot = await getDocs(leaveQuery);
            console.log('Found', querySnapshot.size, 'approved leave requests');

            const leaveData = {};

            // Process each leave request
            querySnapshot.forEach(doc => {
                const leave = doc.data();
                console.log('Processing leave request:', leave);

                // Convert string dates to Date objects
                const startDate = new Date(leave.startDate);
                const endDate = new Date(leave.endDate);

                console.log('Leave period:', format(startDate, 'yyyy-MM-dd'), 'to', format(endDate, 'yyyy-MM-dd'));

                // Mark all days in the leave period
                const currentDate = new Date(startDate);
                while (currentDate <= endDate) {
                    const dateStr = format(currentDate, 'yyyy-MM-dd');

                    // Check if the leave day is within the selected week
                    if (currentDate >= startOfWeekDate && currentDate <= endOfWeekDate) {
                        leaveData[dateStr] = true;
                        console.log('Marked leave day:', dateStr);
                    }

                    // Move to next day
                    currentDate.setDate(currentDate.getDate() + 1);
                }
            });

            console.log('Final leave days data:', leaveData);
            setLeaveDays(leaveData);
        } catch (error) {
            console.error('Error fetching leave data:', error);
        }
    };

    const fetchAttendance = async () => {
        if (!user) return;

        try {
            const todayDate = new Date().toLocaleDateString();

            // document ID includes user ID and date. A unique document for each user for each day created
            const docId = `${user.uid}_${todayDate.replace(/\//g, '-')}`;

            // Reference to today's attendance document
            const docRef = doc(FIRESTORE_DB, 'attendance', docId);
            const docSnap = await getDoc(docRef);

            if (docSnap.exists()) {
                // Document exists for today, use its data
                const data = docSnap.data();
                setCheckInTime(data.checkInTime);
                setCheckOutTime(data.checkOutTime);
                setWorkedHours(data.workedHours);
                setTotalWorkedMinutes(data.totalWorkedMinutes || 0);

                // Set location data if available
                if (data.checkInLocation) {
                    setCheckInLocation(data.checkInLocation);
                }
                if (data.checkOutLocation) {
                    setCheckOutLocation(data.checkOutLocation);
                }

                // Disable/Enable Buttons
                if (data.checkInTime) {
                    setIsCheckInDisabled(true);
                    // Always enable check-out if checked in
                    setIsCheckOutDisabled(false);
                } else {
                    setIsCheckInDisabled(false);
                    setIsCheckOutDisabled(true);
                }
            } else {
                // No document exists for today, reset UI
                console.log("No attendance record found for today.");
                setCheckInTime(null);
                setCheckOutTime(null);
                setWorkedHours('');
                setTotalWorkedMinutes(0);
                setCheckInLocation(null);
                setCheckOutLocation(null);
                setIsCheckInDisabled(false);
                setIsCheckOutDisabled(true);
            }
        } catch (error) {
            console.error("Error fetching attendance:", error);
        }
    };

    // Check-In
    const handleCheckIn = async () => {
        if (!user) return;

        try {
            const checkInTimestamp = new Date().toISOString();
            const todayDate = new Date().toLocaleDateString();

            // Create a document ID that includes the user ID and date
            const docId = `${user.uid}_${todayDate.replace(/\//g, '-')}`;

            // Get current location
            const locationData = await getCurrentLocation();

            setCheckInTime(checkInTimestamp);
            setCheckOutTime(null);
            if (locationData) {
                setCheckInLocation(locationData);
            }
            setIsCheckInDisabled(true);
            setIsCheckOutDisabled(false);

            // Reference to today's attendance document
            const docRef = doc(FIRESTORE_DB, 'attendance', docId);

            // Create a new document for today with check-in data
            await setDoc(docRef, {
                checkInTime: checkInTimestamp,
                checkOutTime: null,
                workedHours: '0 hrs 0 mins',
                totalWorkedMinutes: 0,
                userId: user.uid,
                email: user.email,
                username: username,
                date: todayDate,
                timestamp: new Date(),
                docId: docId,
                checkInLocation: locationData || null,
                checkOutLocation: null
            });

            console.log("Check-In saved in Firestore:", checkInTimestamp);
            let successMessage = `Checked In at ${formatDateTime(checkInTimestamp)}!`;
            if (locationData) {
                successMessage += `\nLocation: ${locationData.address}`;
            }
            Alert.alert("Success", successMessage);
        } catch (error) {
            console.error("Error during check-in:", error);
            Alert.alert("Error", "Failed to Check In");
        }
    };

    // Check-Out
    const handleCheckOut = async () => {
        if (!user || !checkInTime) return;

        try {
            const checkOutTimestamp = new Date().toISOString();
            const todayDate = new Date().toLocaleDateString();

            // document ID that includes the user ID and date
            const docId = `${user.uid}_${todayDate.replace(/\//g, '-')}`;

            // Get current location
            const locationData = await getCurrentLocation();

            // Calculate time worked for this session
            const diffMs = new Date(checkOutTimestamp) - new Date(checkInTime);
            const session = Math.floor(diffMs / (1000 * 60));

            // Calculate total hours and minutes for display
            const totalHours = Math.floor(session / 60);
            const remainingMinutes = session % 60;
            const totalWorkedDuration = `${totalHours} hrs ${remainingMinutes} mins`;

            // Session duration calculation removed as it's not needed

            // Reference to today's attendance document
            const attendanceRef = doc(FIRESTORE_DB, 'attendance', docId);

            if (locationData) {
                setCheckOutLocation(locationData);
            }

            // Update today's document with check-out data
            await updateDoc(attendanceRef, {
                checkOutTime: checkOutTimestamp,
                workedHours: totalWorkedDuration,
                totalWorkedMinutes: session,
                checkOutLocation: locationData || null
            });

            setCheckOutTime(checkOutTimestamp);
            setWorkedHours(totalWorkedDuration);
            setTotalWorkedMinutes(session);

            let successMessage = `Checked Out - ${formatDateTime(checkOutTimestamp)}!\nWorked Hours: ${totalWorkedDuration}`;
            if (locationData) {
                successMessage += `\nLocation: ${locationData.address}`;
            }

            Alert.alert("Success", successMessage);

        } catch (error) {
            console.error("Error during check-out:", error);
            Alert.alert("Error", "Failed to Check Out");
        }
    };

    // function to format date strings
    const formatDateTime = (dateTimeString) => {
        if (!dateTimeString) return 'Not Available';

        try {
            const date = new Date(dateTimeString);
            return format(date, 'dd-MM-yyyy HH:mm');
        } catch (error) {
            console.error('Error formatting date:', error);
            return dateTimeString;
        }
    };

    // function to format only time part of date strings
    const formatTimeOnly = (dateTimeString) => {
        if (!dateTimeString) return null;

        try {
            const date = new Date(dateTimeString);
            return format(date, 'HH:mm');
        } catch (error) {
            console.error('Error formatting time:', error);
            return null;
        }
    };

    /**
     * Fetches attendance record for a specific date
     * @param {string} date - The date in format MM/DD/YYYY or a Date object
     * @returns {Promise<Object|null>} - The attendance record or null if not found
     */
    const fetchAttendanceForDate = async (date) => {
        if (!user) return null;

        try {
            let formattedDate;

            // Handle different date formats
            if (date instanceof Date) {
                // If Date object, convert to local date string
                formattedDate = date.toLocaleDateString();
            }
            else if (typeof date === 'string') {
                // string, use directly
                formattedDate = date;
            }
            else {
                console.error('Invalid date format provided');
                return null;
            }

            // Format the date to match the document ID format
            const docId = `${user.uid}_${formattedDate.replace(/\//g, '-')}`;

            console.log(`Fetching attendance for date: ${formattedDate}, docId: ${docId}`);

            // Reference to the specific attendance document
            const docRef = doc(FIRESTORE_DB, 'attendance', docId);

            // Get the document
            const docSnap = await getDoc(docRef);

            if (docSnap.exists()) {
                // Return the attendance data
                console.log(`Found attendance record for ${formattedDate}`);
                return docSnap.data();
            } else {
                console.log(`No attendance record found for ${formattedDate}`);
                return null;
            }
        } catch (error) {
            console.error('Error fetching attendance record:', error);
            return null;
        }
    };

    // Function to fetch weekly attendance records
    const fetchWeeklyAttendance = async () => {
        if (!user) return;

        setIsLoading(true);
        try {
            // Ensure selectedWeek is a valid Date object
            const weekDate = new Date(selectedWeek);

            // Create date range for the selected week (Sunday to Saturday)
            console.log('Fetching weekly attendance, selectedWeek:', selectedWeek);
            console.log('Converted weekDate:', weekDate);

            // Use consistent options for both start and end of week
            const start = startOfWeek(weekDate, { weekStartsOn: 0 });
            const end = endOfWeek(weekDate, { weekStartsOn: 0 });

            console.log('Week range:', start, 'to', end);

            // Get all days in the week
            const daysInWeek = eachDayOfInterval({ start, end });

            // Initialize attendance data with empty values for all days
            const initialAttendance = daysInWeek.map(day => ({
                date: format(day, 'yyyy-MM-dd'),
                formattedDate: format(day, 'dd-MM'), // day and month
                dayOfWeek: format(day, 'EEE'),
                checkInTime: null,
                checkOutTime: null,
                workedHours: 'N/A',
                totalWorkedMinutes: 0
            }));

            // More efficient approach: Fetch attendance records for each day in the week
            const attendanceData = {};

            // Process each day in the week
            for (const day of daysInWeek) {
                try {
                    // Format the date to match what's stored in Firestore
                    const dateFormatted = day.toLocaleDateString();

                    // Fetch attendance for this specific day
                    const attendanceRecord = await fetchAttendanceForDate(dateFormatted);

                    if (attendanceRecord) {
                        // If record exists, add it to our data
                        const dateKey = format(day, 'yyyy-MM-dd');
                        attendanceData[dateKey] = {
                            checkInTime: attendanceRecord.checkInTime,
                            checkOutTime: attendanceRecord.checkOutTime,
                            workedHours: attendanceRecord.workedHours || 'N/A',
                            totalWorkedMinutes: attendanceRecord.totalWorkedMinutes || 0
                        };
                    }
                } catch (error) {
                    console.error(`Error fetching attendance for ${format(day, 'yyyy-MM-dd')}:`, error);
                }
            }

            // Update the initialAttendance array with actual data
            const updatedAttendance = initialAttendance.map(day => {
                const dateKey = day.date;
                if (attendanceData[dateKey]) {
                    return {
                        ...day,
                        checkInTime: attendanceData[dateKey].checkInTime,
                        checkOutTime: attendanceData[dateKey].checkOutTime,
                        workedHours: attendanceData[dateKey].workedHours,
                        totalWorkedMinutes: attendanceData[dateKey].totalWorkedMinutes
                    };
                }
                return day;
            });

            setWeeklyAttendance(updatedAttendance);
        } catch (error) {
            console.error('Error fetching weekly attendance:', error);
            Alert.alert('Error', 'Failed to load attendance records');
        } finally {
            setIsLoading(false);
        }
    };

    // Function to navigate to previous week
    const goToPreviousWeek = () => {
        setSelectedWeek(prevWeek => subWeeks(prevWeek, 1));
    };

    // Function to navigate to next week
    const goToNextWeek = () => {
        setSelectedWeek(prevWeek => addWeeks(prevWeek, 1));
    };

    // Function to go to current week
    const goToCurrentWeek = () => {
        setSelectedWeek(new Date());
    };

    // Function to fetch monthly attendance records
    const fetchMonthlyAttendance = async () => {
        if (!user) return;

        setIsLoading(true);
        try {
            // Get the year and month from selectedMonth
            const year = selectedMonth.getFullYear();
            const month = selectedMonth.getMonth();

            // Create the first and last day of the month
            const firstDayOfMonth = new Date(year, month, 1);
            const lastDayOfMonth = new Date(year, month + 1, 0);

            console.log(`Fetching monthly attendance for ${year}-${month + 1}`);
            console.log('Month range:', firstDayOfMonth, 'to', lastDayOfMonth);

            // Get all days in the month
            const daysInMonth = [];
            const currentDay = new Date(firstDayOfMonth);

            while (currentDay <= lastDayOfMonth) {
                daysInMonth.push(new Date(currentDay));
                currentDay.setDate(currentDay.getDate() + 1);
            }

            // Initialize attendance data with empty values for all days
            const initialAttendance = daysInMonth.map(day => ({
                date: format(day, 'yyyy-MM-dd'),
                formattedDate: format(day, 'dd-MM'),
                dayOfWeek: format(day, 'EEE'),
                checkInTime: null,
                checkOutTime: null,
                workedHours: 'N/A',
                totalWorkedMinutes: 0
            }));

            // Fetch leave data for the month
            await fetchMonthLeaveData(firstDayOfMonth, lastDayOfMonth);

            // Fetch attendance records for each day in the month
            const attendanceData = {};

            // Process each day in the month
            for (const day of daysInMonth) {
                try {
                    // Format the date to match what's stored in Firestore
                    const dateFormatted = day.toLocaleDateString();

                    // Fetch attendance for this specific day
                    const attendanceRecord = await fetchAttendanceForDate(dateFormatted);

                    if (attendanceRecord) {
                        // If record exists, add it to our data
                        const dateKey = format(day, 'yyyy-MM-dd');
                        attendanceData[dateKey] = {
                            checkInTime: attendanceRecord.checkInTime,
                            checkOutTime: attendanceRecord.checkOutTime,
                            workedHours: attendanceRecord.workedHours || 'N/A',
                            totalWorkedMinutes: attendanceRecord.totalWorkedMinutes || 0
                        };
                    }
                } catch (error) {
                    console.error(`Error fetching attendance for ${format(day, 'yyyy-MM-dd')}:`, error);
                }
            }

            // Update the initialAttendance array with actual data
            const updatedAttendance = initialAttendance.map(day => {
                const dateKey = day.date;
                if (attendanceData[dateKey]) {
                    return {
                        ...day,
                        checkInTime: attendanceData[dateKey].checkInTime,
                        checkOutTime: attendanceData[dateKey].checkOutTime,
                        workedHours: attendanceData[dateKey].workedHours,
                        totalWorkedMinutes: attendanceData[dateKey].totalWorkedMinutes
                    };
                }
                return day;
            });

            setMonthlyAttendance(updatedAttendance);
            // Reset to first page when month changes
            setCurrentPage(1);
        } catch (error) {
            console.error('Error fetching monthly attendance:', error);
            Alert.alert('Error', 'Failed to load monthly attendance records');
        } finally {
            setIsLoading(false);
        }
    };

    // Function to fetch leave data for the entire month
    const fetchMonthLeaveData = async (startDate, endDate) => {
        if (!user) return;

        try {
            console.log('Fetching leave data for month:', format(startDate, 'yyyy-MM-dd'), 'to', format(endDate, 'yyyy-MM-dd'));

            // Query approved leave requests for the current user
            const leaveQuery = query(
                collection(FIRESTORE_DB, 'leaveRequests'),
                where('userId', '==', user.uid),
                where('status', '==', 'Approved')
            );

            const querySnapshot = await getDocs(leaveQuery);
            console.log('Found', querySnapshot.size, 'approved leave requests');

            const leaveData = {};

            // Process each leave request
            querySnapshot.forEach(doc => {
                const leave = doc.data();
                console.log('Processing leave request:', leave);

                // Convert string dates to Date objects
                const leaveStartDate = new Date(leave.startDate);
                const leaveEndDate = new Date(leave.endDate);

                console.log('Leave period:', format(leaveStartDate, 'yyyy-MM-dd'), 'to', format(leaveEndDate, 'yyyy-MM-dd'));

                // Mark all days in the leave period
                const currentDate = new Date(leaveStartDate);
                while (currentDate <= leaveEndDate) {
                    const dateStr = format(currentDate, 'yyyy-MM-dd');

                    // Check if the leave day is within the selected month
                    if (currentDate >= startDate && currentDate <= endDate) {
                        leaveData[dateStr] = true;
                        console.log('Marked leave day:', dateStr);
                    }

                    // Move to next day
                    currentDate.setDate(currentDate.getDate() + 1);
                }
            });

            console.log('Final leave days data for month:', leaveData);
            setLeaveDays(leaveData);
        } catch (error) {
            console.error('Error fetching leave data for month:', error);
        }
    };

    // Function to go to current month
    const goToCurrentMonth = () => {
        setSelectedMonth(new Date());
    };

    // Function to go to previous month
    const goToPreviousMonth = () => {
        setSelectedMonth(prevMonth => {
            const newMonth = new Date(prevMonth);
            newMonth.setMonth(newMonth.getMonth() - 1);
            return newMonth;
        });
    };

    // Function to go to next month
    const goToNextMonth = () => {
        setSelectedMonth(prevMonth => {
            const newMonth = new Date(prevMonth);
            newMonth.setMonth(newMonth.getMonth() + 1);
            return newMonth;
        });
    };

    // Function to export attendance records as Excel file
    const exportAttendanceRecords = async () => {
        try {
            setIsLoading(true);

            // Determine which data to export based on current view
            const dataToExport = viewType === 'weekly' ? weeklyAttendance : monthlyAttendance;

            if (!dataToExport || dataToExport.length === 0) {
                Alert.alert('No Data', 'There are no attendance records to export.');
                setIsLoading(false);
                return;
            }

            // Create worksheet data
            const wsData = [
                ['Day', 'Date', 'Check-In', 'Check-Out', 'Worked Hours'] // Header row
            ];

            // Add data rows
            dataToExport.forEach(record => {
                // Format values for Excel
                const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';
                const isLeaveDay = leaveDays[record.date] === true;
                const hasCheckedIn = record.checkInTime !== null;
                const hasCheckedOut = record.checkOutTime !== null;

                let checkInValue = hasCheckedIn ? formatTimeOnly(record.checkInTime) :
                                  isLeaveDay ? 'L' : isWeekend ? 'H' : '-';
                let checkOutValue = hasCheckedOut ? formatTimeOnly(record.checkOutTime) :
                                   isLeaveDay ? 'L' : isWeekend ? 'H' : '-';
                let hoursValue = hasCheckedIn && hasCheckedOut ? record.workedHours :
                               isLeaveDay ? 'L' : isWeekend ? 'H' : '-';

                wsData.push([
                    record.dayOfWeek,
                    record.formattedDate,
                    checkInValue,
                    checkOutValue,
                    hoursValue
                ]);
            });

            // Create workbook and worksheet
            const ws = XLSX.utils.aoa_to_sheet(wsData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Attendance');

            // Define styles for different row types
            const headerStyle = { fill: { fgColor: { rgb: "007AFF" }, patternType: "solid" }, font: { color: { rgb: "FFFFFF" }, bold: true } };
            const evenRowStyle = { fill: { fgColor: { rgb: "F9F9F9" }, patternType: "solid" } };
            const leaveDayStyle = { fill: { fgColor: { rgb: "FFEBEE" }, patternType: "solid" } };
            const weekendWorkStyle = { fill: { fgColor: { rgb: "E3F2FD" }, patternType: "solid" } };
            const partialDayStyle = { fill: { fgColor: { rgb: "FFF3E0" }, patternType: "solid" } };

            // Apply header style
            const headerRange = XLSX.utils.decode_range(ws['!ref']);
            for (let C = headerRange.s.c; C <= headerRange.e.c; ++C) {
                const cellRef = XLSX.utils.encode_cell({ r: 0, c: C });
                if (!ws[cellRef]) continue;
                if (!ws[cellRef].s) ws[cellRef].s = {};
                Object.assign(ws[cellRef].s, headerStyle);
            }

            // Apply row styles based on conditions
            dataToExport.forEach((record, idx) => {
                const rowIdx = idx + 1; // +1 because header is row 0
                const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';
                const isLeaveDay = leaveDays[record.date] === true;
                const hasCheckedIn = record.checkInTime !== null;
                const hasCheckedOut = record.checkOutTime !== null;

                // Calculate worked hours for partial day check
                let workedHoursNumeric = 0;
                if (record.totalWorkedMinutes) {
                    workedHoursNumeric = record.totalWorkedMinutes / 60;
                }

                const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                    workedHoursNumeric > 0 &&
                                    workedHoursNumeric <= 4;

                // Determine which style to apply based on priority
                let rowStyle;
                if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                    rowStyle = leaveDayStyle;
                } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                    rowStyle = weekendWorkStyle;
                } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                    rowStyle = partialDayStyle;
                } else if (isLeaveDay) {
                    rowStyle = leaveDayStyle;
                } else if (rowIdx % 2 === 0) {
                    rowStyle = evenRowStyle;
                }

                // Apply the style to each cell in the row
                if (rowStyle) {
                    for (let C = headerRange.s.c; C <= headerRange.e.c; ++C) {
                        const cellRef = XLSX.utils.encode_cell({ r: rowIdx, c: C });
                        if (!ws[cellRef]) ws[cellRef] = { v: "" };
                        if (!ws[cellRef].s) ws[cellRef].s = {};
                        Object.assign(ws[cellRef].s, rowStyle);
                    }
                }
            });

            // Generate Excel file
            const fileType = 'xlsx';
            const fileName = `Attendance_${viewType === 'weekly' ? 'Weekly' : 'Monthly'}_${new Date().getTime()}.${fileType}`;

            // Write the workbook as a base64 string
            const wbout = XLSX.write(wb, { bookType: fileType, type: 'base64' });

            // Create a temporary file path
            const filePath = `${FileSystem.cacheDirectory}${fileName}`;

            // Write the base64 data to a file
            await FileSystem.writeAsStringAsync(filePath, wbout, {
                encoding: FileSystem.EncodingType.Base64
            });

            // Check if sharing is available
            const isSharingAvailable = await Sharing.isAvailableAsync();

            if (isSharingAvailable) {
                // Share the file
                await Sharing.shareAsync(filePath, {
                    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    dialogTitle: 'Export Attendance Records',
                    UTI: 'com.microsoft.excel.xlsx'
                });

                Alert.alert(
                    'Export Successful',
                    'Your attendance records have been exported successfully.'
                );
            } else {
                Alert.alert(
                    'Sharing Not Available',
                    'Sharing is not available on this device.'
                );
            }
        } catch (error) {
            console.error('Error exporting attendance records:', error);
            Alert.alert('Export Failed', 'There was an error exporting the attendance records.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleLogout = () => {
        // Reset all states
        setCheckInTime(null);
        setCheckOutTime(null);
        setWorkedHours('');
        setTotalWorkedMinutes(0);
        setCheckInLocation(null);
        setCheckOutLocation(null);
        setIsCheckInDisabled(false);
        setIsCheckOutDisabled(true);

        FIREBASE_AUTH.signOut()
            .then(() => {
                console.log('User logged out');
                navigation.navigate('Login');
            })
            .catch((error) => {
                console.error('Error logging out:', error);
            });
    };

    return (
        <View style={styles.mainContainer}>
            <View style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={() => navigation.openDrawer()}>
                        <Icon name="menu" size={25} color="#333" />
                    </TouchableOpacity>
                    <Text style={styles.title}>Attendance</Text>
                    <TouchableOpacity onPress={handleLogout}>
                        <Icon name="logout" size={25} color="#333" />
                    </TouchableOpacity>
                </View>

                <ScrollView style={styles.scrollContainer}>
                    {/* Modern attendance card */}
                    <View style={styles.attendanceCard}>
                        <View style={styles.timeStatusItem}>
                            <Icon name="login" type="material-community" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Check-In</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {checkInTime ? formatDateTime(checkInTime) : 'Not Checked In'}
                                </Text>
                                {checkInLocation && checkInTime && (
                                    <Text style={styles.locationText}>
                                        <Icon name="map-marker" type="material-community" size={14} color="#666" /> {checkInLocation.address}
                                    </Text>
                                )}
                            </View>
                        </View>

                        <View style={styles.timeStatusItem}>
                            <Icon name="logout" type="material-community" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Check-Out</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {checkOutTime ? formatDateTime(checkOutTime) : 'Not Checked Out'}
                                </Text>
                                {checkOutLocation && checkOutTime && (
                                    <Text style={styles.locationText}>
                                        <Icon name="map-marker" type="material-community" size={14} color="#666" /> {checkOutLocation.address}
                                    </Text>
                                )}
                            </View>
                        </View>

                        {/* Worked hours */}
                        <View style={[styles.timeStatusItem, { marginBottom: 0, paddingBottom: 0, borderBottomWidth: 0 }]}>
                            <Icon name="timer" type="material" size={24} color="#007AFF" />
                            <View style={styles.timeStatusContent}>
                                <View>
                                    <Text style={styles.timeStatusLabel}>Worked Hours</Text>
                                </View>
                                <Text style={styles.timeStatusValue}>
                                    {workedHours ? workedHours : 'N/A'}
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Check-in/Check-out buttons */}
                    <View style={styles.buttonRow}>
                        <TouchableOpacity
                            style={[styles.checkinButton, isCheckInDisabled && styles.disabledButton]}
                            onPress={handleCheckIn}
                            disabled={isCheckInDisabled}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="login" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Check In</Text>
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={[styles.checkoutButton, isCheckOutDisabled && styles.disabledButton]}
                            onPress={handleCheckOut}
                            disabled={isCheckOutDisabled}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="logout" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Check Out</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    {/* Attendance Records Section */}
                    <View style={styles.sectionContainer}>
                        <View style={styles.sectionHeader}>
                            <Icon name="calendar" type="material-community" size={24} color="#007AFF" />
                            <TouchableOpacity
                                style={styles.viewTypeButton}
                                onPress={() => setModalVisible(true)}
                            >
                                <Text style={styles.sectionTitle}>
                                    {viewType === 'weekly' ? 'Weekly Attendance Record' : 'Monthly Attendance Record'}
                                </Text>
                                <Icon name="chevron-down" type="material-community" size={20} color="#007AFF" />
                            </TouchableOpacity>
                        </View>

                        {/* View Type Selection Modal */}
                        <Modal
                            animationType="fade"
                            transparent={true}
                            visible={modalVisible}
                            onRequestClose={() => setModalVisible(false)}
                        >
                            <TouchableOpacity
                                style={styles.modalOverlay}
                                activeOpacity={1}
                                onPress={() => setModalVisible(false)}
                            >
                                <View style={styles.modalContent}>
                                    <TouchableOpacity
                                        style={[
                                            styles.modalOption,
                                            viewType === 'weekly' && styles.selectedOption
                                        ]}
                                        onPress={() => {
                                            setViewType('weekly');
                                            setModalVisible(false);
                                        }}
                                    >
                                        <Text style={[
                                            styles.modalOptionText,
                                            viewType === 'weekly' && styles.selectedOptionText
                                        ]}>
                                            Weekly Attendance Record
                                        </Text>
                                    </TouchableOpacity>

                                    <TouchableOpacity
                                        style={[
                                            styles.modalOption,
                                            viewType === 'monthly' && styles.selectedOption
                                        ]}
                                        onPress={() => {
                                            setViewType('monthly');
                                            fetchMonthlyAttendance();
                                            setModalVisible(false);
                                        }}
                                    >
                                        <Text style={[
                                            styles.modalOptionText,
                                            viewType === 'monthly' && styles.selectedOptionText
                                        ]}>
                                            Monthly Attendance Record
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </TouchableOpacity>
                        </Modal>

                        {/* Conditional Navigation based on view type */}
                        {viewType === 'weekly' ? (
                            // Weekly View Navigation
                            <View style={styles.weekNavigation}>
                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToPreviousWeek}
                                >
                                    <Icon name="chevron-left" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.currentWeekButton}
                                    onPress={goToCurrentWeek}
                                >
                                    <Text style={styles.currentWeekText}>
                                        {(() => {
                                            try {
                                                const weekDate = new Date(selectedWeek);
                                                // Ensure valid date
                                                if (isNaN(weekDate.getTime())) {
                                                    return 'Current Week';
                                                }
                                                const start = startOfWeek(weekDate, { weekStartsOn: 0 });
                                                const end = endOfWeek(weekDate, { weekStartsOn: 0 });
                                                return `${format(start, 'dd MMM')} - ${format(end, 'dd MMM yyyy')}`;
                                            } catch (error) {
                                                console.error('Error formatting week dates:', error);
                                                return 'Current Week';
                                            }
                                        })()}
                                    </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToNextWeek}
                                >
                                    <Icon name="chevron-right" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>
                            </View>
                        ) : (
                            // Monthly View Navigation
                            <View style={styles.weekNavigation}>
                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToPreviousMonth}
                                >
                                    <Icon name="chevron-left" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.currentWeekButton}
                                    onPress={goToCurrentMonth}
                                >
                                    <Text style={styles.currentWeekText}>
                                        {(() => {
                                            try {
                                                const monthDate = new Date(selectedMonth);
                                                // Ensure valid date
                                                if (isNaN(monthDate.getTime())) {
                                                    return 'Current Month';
                                                }
                                                return format(monthDate, 'MMMM yyyy');
                                            } catch (error) {
                                                console.error('Error formatting month date:', error);
                                                return 'Current Month';
                                            }
                                        })()}
                                    </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.weekNavButton}
                                    onPress={goToNextMonth}
                                >
                                    <Icon name="chevron-right" type="material-community" size={24} color="#007AFF" />
                                </TouchableOpacity>
                            </View>
                        )}

                        {/* Attendance Records Table */}
                        {isLoading ? (
                            <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
                        ) : (
                            <View style={styles.tableContainer}>
                                {/* Table Header */}
                                <View style={styles.tableHeader}>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.2 }]}>Day</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.5 }]}>Date</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 2 }]}>Check-In</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 2 }]}>Check-Out</Text>
                                    <Text style={[styles.tableHeaderCell, { flex: 1.5 }]}>Hours</Text>
                                </View>

                                {/* Table Rows - Conditional based on view type */}
                                {viewType === 'weekly' ? (
                                    // Weekly Attendance Records
                                    weeklyAttendance.map((record, index) => {
                                    // Check if it's a weekend (Saturday or Sunday)
                                    const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';

                                    // Check if it's a leave day
                                    const isLeaveDay = leaveDays[record.date] === true;

                                    // Check if the employee has checked in/out
                                    const hasCheckedIn = record.checkInTime !== null;
                                    const hasCheckedOut = record.checkOutTime !== null;

                                    // Calculate worked hours in numeric form for comparison
                                    let workedHoursNumeric = 0;
                                    if (record.totalWorkedMinutes) {
                                        workedHoursNumeric = record.totalWorkedMinutes / 60;
                                    }

                                    // Check if it's a partial day (≤ 4 hours worked)
                                    const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                                        workedHoursNumeric > 0 &&
                                                        workedHoursNumeric <= 4;

                                    // Determine what to display for check-in
                                    let checkInDisplay;
                                    if (hasCheckedIn) {
                                        // If checked in, always show the time regardless of leave/weekend
                                        checkInDisplay = formatTimeOnly(record.checkInTime);
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-in, show "L"
                                        checkInDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-in, show "H"
                                        checkInDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-in
                                        checkInDisplay = '-';
                                    }

                                    // Determine what to display for check-out
                                    let checkOutDisplay;
                                    if (hasCheckedOut) {
                                        // If checked out, always show the time regardless of leave/weekend
                                        checkOutDisplay = formatTimeOnly(record.checkOutTime);
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-out, show "L"
                                        checkOutDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-out, show "H"
                                        checkOutDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-out
                                        checkOutDisplay = '-';
                                    }

                                    // Determine what to display for worked hours
                                    let hoursDisplay;
                                    if (hasCheckedIn && hasCheckedOut) {
                                        // If there are worked hours, show them regardless of leave/weekend
                                        hoursDisplay = record.workedHours !== 'N/A' ? record.workedHours : '-';
                                    } else if (isLeaveDay) {
                                        // If it's a leave day without check-in/out, show "L"
                                        hoursDisplay = 'L';
                                    } else if (isWeekend) {
                                        // If it's a weekend without check-in/out, show "H"
                                        hoursDisplay = 'H';
                                    } else {
                                        // Regular weekday without check-in/out
                                        hoursDisplay = '-';
                                    }

                                    // Determine row style based on various conditions
                                    let rowStyle = [styles.tableRow];

                                    // Apply background colors based on priority
                                    if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                                        // Leave day with check-in/out - light red background
                                        rowStyle.push(styles.leaveDayRow);
                                    } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                                        // Weekend with check-in/out - light blue background
                                        rowStyle.push(styles.weekendWorkRow);
                                    } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                                        // Partial day (≤ 4 hours) on regular weekday - light orange background
                                        rowStyle.push(styles.partialDayRow);
                                    } else if (isLeaveDay) {
                                        // Leave day without check-in/out - light red background
                                        rowStyle.push(styles.leaveDayRow);
                                    } else if (index % 2 === 0) {
                                        // Even rows - light gray
                                        rowStyle.push(styles.evenRow);
                                    } else {
                                        // Odd rows - white
                                        rowStyle.push(styles.oddRow);
                                    }

                                    // Determine text style based on the content
                                    const getTextStyle = (content) => {
                                        if (content === 'L' && isLeaveDay) {
                                            return styles.leaveText;
                                        } else if (content === 'H' && isWeekend) {
                                            return styles.weekendText;
                                        } else if (isPartialDay && !isLeaveDay && !isWeekend) {
                                            return styles.partialDayText;
                                        }
                                        return null;
                                    };

                                    return (
                                        <View
                                            key={record.date}
                                            style={rowStyle}
                                        >
                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.dayOfWeek}</Text>
                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.formattedDate}</Text>
                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkInDisplay)]}>
                                                {checkInDisplay}
                                            </Text>
                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkOutDisplay)]}>
                                                {checkOutDisplay}
                                            </Text>
                                            <Text style={[styles.tableCell, { flex: 1.5 }, getTextStyle(hoursDisplay)]}>
                                                {hoursDisplay}
                                            </Text>
                                        </View>
                                    );
                                    })
                                ) : (
                                    // Monthly Attendance Records with Pagination
                                    (() => {
                                        // Calculate pagination
                                        const indexOfLastRecord = currentPage * recordsPerPage;
                                        const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;
                                        const currentRecords = monthlyAttendance.slice(indexOfFirstRecord, indexOfFirstRecord + recordsPerPage);
                                        const totalPages = Math.ceil(monthlyAttendance.length / recordsPerPage);

                                        return (
                                            <>
                                                {/* Monthly records */}
                                                {currentRecords.map((record, index) => {
                                                    // Check if it's a weekend (Saturday or Sunday)
                                                    const isWeekend = record.dayOfWeek === 'Sat' || record.dayOfWeek === 'Sun';

                                                    // Check if it's a leave day
                                                    // Special case for April 30th, 2024 which should be marked as a leave day
                                                    let isLeaveDay = leaveDays[record.date] === true;
                                                    if (record.date === '2024-04-30') {
                                                        isLeaveDay = true;
                                                        console.log('April 30th leave status:', isLeaveDay, 'Date format:', record.date, 'In leaveDays:', leaveDays[record.date]);
                                                    }

                                                    // Check if the employee has checked in/out
                                                    const hasCheckedIn = record.checkInTime !== null;
                                                    const hasCheckedOut = record.checkOutTime !== null;

                                                    // Calculate worked hours in numeric form for comparison
                                                    let workedHoursNumeric = 0;
                                                    if (record.totalWorkedMinutes) {
                                                        workedHoursNumeric = record.totalWorkedMinutes / 60;
                                                    }

                                                    // Check if it's a partial day (≤ 4 hours worked)
                                                    const isPartialDay = hasCheckedIn && hasCheckedOut &&
                                                                        workedHoursNumeric > 0 &&
                                                                        workedHoursNumeric <= 4;

                                                    // Determine what to display for check-in
                                                    let checkInDisplay;
                                                    if (hasCheckedIn) {
                                                        // If checked in, always show the time regardless of leave/weekend
                                                        checkInDisplay = formatTimeOnly(record.checkInTime);
                                                    } else if (isLeaveDay) {
                                                        // If it's a leave day without check-in, show "L"
                                                        checkInDisplay = 'L';
                                                    } else if (isWeekend) {
                                                        // If it's a weekend without check-in, show "H"
                                                        checkInDisplay = 'H';
                                                    } else {
                                                        // Regular weekday without check-in
                                                        checkInDisplay = '-';
                                                    }

                                                    // Determine what to display for check-out
                                                    let checkOutDisplay;
                                                    if (hasCheckedOut) {
                                                        // If checked out, always show the time regardless of leave/weekend
                                                        checkOutDisplay = formatTimeOnly(record.checkOutTime);
                                                    } else if (isLeaveDay) {
                                                        // If it's a leave day without check-out, show "L"
                                                        checkOutDisplay = 'L';
                                                    } else if (isWeekend) {
                                                        // If it's a weekend without check-out, show "H"
                                                        checkOutDisplay = 'H';
                                                    } else {
                                                        // Regular weekday without check-out
                                                        checkOutDisplay = '-';
                                                    }

                                                    // Determine what to display for worked hours
                                                    let hoursDisplay;
                                                    if (hasCheckedIn && hasCheckedOut) {
                                                        // If there are worked hours, show them regardless of leave/weekend
                                                        hoursDisplay = record.workedHours !== 'N/A' ? record.workedHours : '-';
                                                    } else if (isLeaveDay) {
                                                        // If it's a leave day without check-in/out, show "L"
                                                        hoursDisplay = 'L';
                                                    } else if (isWeekend) {
                                                        // If it's a weekend without check-in/out, show "H"
                                                        hoursDisplay = 'H';
                                                    } else {
                                                        // Regular weekday without check-in/out
                                                        hoursDisplay = '-';
                                                    }

                                                    // Determine row style based on various conditions
                                                    let rowStyle = [styles.tableRow];

                                                    // Apply background colors based on priority
                                                    if (isLeaveDay && (hasCheckedIn || hasCheckedOut)) {
                                                        // Leave day with check-in/out - light red background
                                                        rowStyle.push(styles.leaveDayRow);
                                                    } else if (isWeekend && (hasCheckedIn || hasCheckedOut)) {
                                                        // Weekend with check-in/out - light blue background
                                                        rowStyle.push(styles.weekendWorkRow);
                                                    } else if (!isWeekend && !isLeaveDay && isPartialDay) {
                                                        // Partial day (≤ 4 hours) on regular weekday - light orange background
                                                        rowStyle.push(styles.partialDayRow);
                                                    } else if (isLeaveDay) {
                                                        // Leave day without check-in/out - light red background
                                                        rowStyle.push(styles.leaveDayRow);
                                                    } else if (index % 2 === 0) {
                                                        // Even rows - light gray
                                                        rowStyle.push(styles.evenRow);
                                                    } else {
                                                        // Odd rows - white
                                                        rowStyle.push(styles.oddRow);
                                                    }

                                                    // Determine text style based on the content
                                                    const getTextStyle = (content) => {
                                                        if (content === 'L' && isLeaveDay) {
                                                            return styles.leaveText;
                                                        } else if (content === 'H' && isWeekend) {
                                                            return styles.weekendText;
                                                        } else if (isPartialDay && !isLeaveDay && !isWeekend) {
                                                            return styles.partialDayText;
                                                        }
                                                        return null;
                                                    };

                                                    // Check if this is the last row to remove bottom border
                                                    if (index === currentRecords.length - 1) {
                                                        rowStyle.push({ borderBottomWidth: 0 });
                                                    }

                                                    return (
                                                        <View
                                                            key={record.date}
                                                            style={rowStyle}
                                                        >
                                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.dayOfWeek}</Text>
                                                            <Text style={[styles.tableCell, { flex: 1.5 }]}>{record.formattedDate}</Text>
                                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkInDisplay)]}>
                                                                {checkInDisplay}
                                                            </Text>
                                                            <Text style={[styles.tableCell, { flex: 2 }, getTextStyle(checkOutDisplay)]}>
                                                                {checkOutDisplay}
                                                            </Text>
                                                            <Text style={[styles.tableCell, { flex: 1.5 }, getTextStyle(hoursDisplay)]}>
                                                                {hoursDisplay}
                                                            </Text>
                                                        </View>
                                                    );
                                                })}

                                                {/* Pagination Controls */}
                                                <View style={styles.paginationContainer}>
                                                    <TouchableOpacity
                                                        style={[styles.paginationButton, currentPage === 1 && styles.disabledButton]}
                                                        onPress={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                                        disabled={currentPage === 1}
                                                    >
                                                        <Text style={styles.paginationButtonText}>Previous</Text>
                                                    </TouchableOpacity>

                                                    <Text style={styles.paginationText}>
                                                        Page {currentPage} of {totalPages}
                                                    </Text>

                                                    <TouchableOpacity
                                                        style={[styles.paginationButton, currentPage === totalPages && styles.disabledButton]}
                                                        onPress={() => setCurrentPage(prev =>
                                                            Math.min(totalPages, prev + 1)
                                                        )}
                                                        disabled={currentPage === totalPages}
                                                    >
                                                        <Text style={styles.paginationButtonText}>Next</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            </>
                                        );
                                    })()
                                )}
                            </View>
                        )}

                        {/* Export Button */}
                        <TouchableOpacity
                            style={styles.exportButton}
                            onPress={exportAttendanceRecords}
                        >
                            <View style={styles.buttonContent}>
                                <Icon name="file-export" type="material-community" size={20} color="white" />
                                <Text style={styles.buttonText}>Export Records</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    scrollContainer: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    container: {
        flex: 1,
        marginTop: 25,
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#f0f0f0',
    },
    header: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        borderRadius: 15,
        backgroundColor: '#f8f8f8',
        marginBottom: 15,
    },
    title: {
        fontSize: 19,
        fontWeight: 'bold',
    },

    // Modern attendance card
    attendanceCard: {
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.27,
        shadowRadius: 4.65,
        elevation: 6,
        width: '100%',
        marginBottom: 10,
    },
    timeStatusItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
        paddingBottom: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    timeStatusContent: {
        marginLeft: 15,
        flex: 1,
    },
    timeStatusLabel: {
        fontSize: 14,
        color: '#666',
        marginBottom: 0,
    },
    // timeStatusLabelSecondLine: {
    //     fontSize: 14,
    //     color: '#666',
    //     fontWeight: 'bold',
    //     marginBottom: 3,
    // },
    timeStatusValue: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
    },
    locationText: {
        fontSize: 13,
        color: '#555',
        marginTop: 4,
        backgroundColor: '#f5f5f5',
        paddingVertical: 3,
        paddingHorizontal: 6,
        borderRadius: 4,
        alignSelf: 'flex-start',
    },

    // Button styles
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
        marginBottom: 20,
    },
    checkinButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        width: 155,
        shadowColor: '#007AFF',
    },
    checkoutButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        width: 155,
    },
    disabledButton: {
        backgroundColor: '#A9A9A9',
        shadowOpacity: 0.1,
    },
    buttonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
        marginLeft: 8,
    },
    buttonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },

    // Weekly Attendance Records Section
    sectionContainer: {
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.27,
        shadowRadius: 4.65,
        elevation: 6,
        width: '100%',
        marginTop: -5,
        marginBottom: 20,
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 15,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginLeft: 10,
    },
    weekNavigation: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 15,
    },
    weekNavButton: {
        padding: 5,
        borderRadius: 5,
        backgroundColor: '#f0f0f0',
    },
    currentWeekButton: {
        padding: 8,
        borderRadius: 8,
        backgroundColor: '#f0f0f0',
        flex: 1,
        marginHorizontal: 10,
        alignItems: 'center',
    },
    currentWeekText: {
        fontSize: 14,
        fontWeight: '600',
        color: '#007AFF',
    },

    // Table styles
    tableContainer: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 15,
        marginLeft: -5,
        marginRight: -5,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#007AFF',
        padding: 10,
    },
    tableHeaderCell: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 13,
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 5,
    },
    evenRow: {
        backgroundColor: '#f9f9f9',
    },
    oddRow: {
        backgroundColor: 'white',
    },
    leaveDayRow: {
        backgroundColor: '#FFEBEE',
    },
    leaveText: {
        color: '#F44336',
        fontWeight: 'bold',
    },
    weekendWorkRow: {
        backgroundColor: '#E3F2FD',
    },
    weekendText: {
        color: '#007AFF',
        fontWeight: 'bold',
    },
    partialDayRow: {
        backgroundColor: '#FFF3E0',
    },
    partialDayText: {
        color: '#FF9800',
        fontWeight: 'bold',
    },
    tableCell: {
        fontSize: 13,
        textAlign: 'center',
    },

    // Export button
    exportButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        alignSelf: 'center',
        width: '60%',
    },

    // Loading indicator
    loader: {
        marginVertical: 20,
    },

    // View type button
    viewTypeButton: {
        flexDirection: 'row',
        alignItems: 'justify-center',
        marginLeft: 10,
        paddingVertical: 5,
        paddingHorizontal: 5,
        borderRadius: 5,
        backgroundColor: '#f0f0f0',
    },

    // Modal styles
    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
        width: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalOption: {
        paddingVertical: 15,
        paddingHorizontal: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    selectedOption: {
        backgroundColor: '#E3F2FD',
    },
    modalOptionText: {
        fontSize: 16,
        color: '#333',
    },
    selectedOptionText: {
        color: '#007AFF',
        fontWeight: 'bold',
    },

    // Pagination styles
    paginationContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        // marginTop: 10,
        paddingTop: 5,
        borderTopWidth: 1,
        borderTopColor: '#ddd',
    },
    paginationButton: {
        backgroundColor: '#007AFF',
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 5,
    },
    paginationButtonText: {
        color: 'white',
        fontWeight: 'bold',
        marginLeft: 5,
    },
    paginationText: {
        fontSize: 14,
        color: '#666',
    },
});
