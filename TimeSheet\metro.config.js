// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add extraNodeModules to resolve problematic dependencies
config.resolver.extraNodeModules = {
  // Ensure idb is properly resolved
  'idb': path.resolve(__dirname, 'node_modules/idb'),
};

// Add additional extensions for better compatibility
config.resolver.sourceExts = ['js', 'jsx', 'ts', 'tsx', 'json', 'mjs', 'cjs'];

// Add specific aliases for Firebase
config.resolver.alias = {
  ...config.resolver.alias,
  '@firebase/app': path.resolve(__dirname, 'node_modules/@firebase/app'),
  '@firebase/util': path.resolve(__dirname, 'node_modules/@firebase/util'),
};

module.exports = config;
