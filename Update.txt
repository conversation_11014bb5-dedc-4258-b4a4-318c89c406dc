- Added the Tasks and Drawer Navigation and 
- Firebase storing of the tasks

Update for 4th, 5th and 6th March
- Added Drawer Navigation for Dashboard 
and Tasks screen
- Updated code for Editable tasks and deleting 
task (both from UI and firebase) also added 
checkbox for keeping track of the completion of the tasks
- Adding the tasks appropriately according the 
date and also viewing the tasks added
- Addition, deletion and update of 
tasks from UI and firebase. 

For my timesheet app, sign up and login page is ready and working, then after login, it navigates to user dashboard consisting of calender where you can choose the date and add, view, edit & delete tasks for that specific day. And now, I have a new requirement to introduce role type, like user and admin. This should be secure. I'm using firebase. My idea is simple, when user logs in, he is by default assigned the role of user but in the firebase, I can change the role type to 'ADMIN' so that only the one has access to firebase can handle the admin and user thing. And the admin screen should have a dropdown menu that has all the users and admin can select any one user and access their account and view their tasks 

Update for 7th March
- Study and a bit of research, for 
  admin account and firebase rules 
  for admin account
- Started with admin account code, 
  and added firebase rules

Update for 10th March
- Coded for admin account, able 
  to retrieve the users 
- Firestore has two collections, 
  for tasks and users 
  (for filtering via firebase)
- Tasks retrieval of users is yet to 
  be done in admin account

Update for 11th march
- Updated code, able to retrieve users 
  with their tasks on specific dates

12th March
- In user & admin account, removed the 
  side arrows for calender navigation
- and integrated the the dropdown for month 
  and year
- Hamburger menu in ADMIN account should 
  have two things:
	“View Tasks” - able to view users’ tasks
	“Manage Tasks” - manage admin’s tasks

13th March
- In the Tasks page of user and admin add 
  a icon ‘+’, selecting the icon will add 
  new task 
- While user has completed the task, 
  disable the edit option for tasks

14th March
- Also, added a loader for getting while the 
  content is being loaded 
- Added functionality for admin could 
  add tasks on user side but user can’t 
  delete it
- Add two stages of tasks completion, 
  yellow (in progress), green (completed) 

19th March
Study and work on state management 
with firebase

20th March
Disabled the back navigation component 
for ensuring security

21st March
state management with back navigation 
and access of admin and users account

24th March
Leave application feature user side 
and added Modal for leave application

25th March
- Leave application feature admin side 
and added Modal for leave application 
- Added leave application collection in 
firestore

26th March
Worked on errors, had to figure out about 
onePicker error for setting of start date 
and end date of leave

27th March
Updated and added status of the leave 
applications 
Updated and added status for tracking the  
leave applications for admin and user side

28th March
Sorted some firebase errors and worked on 
filter component in admin side of leave 
applications 

31st March
Updated and worked on leave application, 
admin and user side. Updated the UI,
filter and reset

1st April
Researching about check-in and check-out 
component and how to add it in an
application
